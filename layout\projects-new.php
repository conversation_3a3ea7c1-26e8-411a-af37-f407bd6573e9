<?php
require($_SERVER['DOCUMENT_ROOT'] . '/config/conn.php');

// دالة لجلب طرق الدفع المتاحة من قاعدة البيانات
function getPaymentMethods()
{
    global $conn;

    $stmt = $conn->prepare('SELECT method_name FROM wallet_addresses');
    $stmt->execute();
    $result = $stmt->get_result();

    $methods = [];
    while ($row = $result->fetch_assoc()) {
        $methods[] = $row['method_name'];
    }

    $stmt->close();
    return $methods;
}

// دالة لجلب العملات من قاعدة البيانات
function getCurrencies()
{
    global $conn;

    $currencies = [];

    // محاولة جلب العملات من جدول currencies
    $stmt = $conn->prepare('SELECT symbol, name_ar, value FROM currencies ORDER BY id ASC');
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $currencies[] = [
                'symbol' => $row['symbol'],
                'name_ar' => $row['name_ar'],
                'value' => $row['value']
            ];
        }
    } else {
        // إذا لم يتم العثور على عملات، استخدم القيم الافتراضية
        $currencies = [
            ['symbol' => 'USD', 'name_ar' => 'دولار أمريكي', 'value' => 1],
            ['symbol' => 'EUR', 'name_ar' => 'يورو', 'value' => 1],
            ['symbol' => 'EGP', 'name_ar' => 'جنيه مصري', 'value' => 50],
            ['symbol' => 'JOD', 'name_ar' => 'دينار أردني', 'value' => 0.70]
        ];
    }

    $stmt->close();
    return $currencies;
}

// جلب طرق الدفع
$paymentMethods = getPaymentMethods();

// جلب العملات
$currencies = getCurrencies();

// إنشاء مصفوفة لأسعار الأسهم لاستخدامها في JavaScript
$sharePrices = [];
foreach ($currencies as $currency) {
    $sharePrices[$currency['symbol']] = $currency['value'];
}
?>

<!-- روابط مكتبة Swiper JS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

<style>
    /* تنسيقات عامة */
    .rtl {
        direction: rtl;
        text-align: right;
    }

    /* تنسيقات Swiper للمشاريع العاجلة */
    .urgent-projects-swiper {
        width: 100%;
        padding-bottom: 40px;
    }

    .urgent-projects-swiper .swiper-slide {
        display: flex;
        justify-content: center;
    }

    .urgent-projects-swiper .swiper-pagination {
        bottom: 0;
    }

    .urgent-projects-swiper .swiper-pagination-bullet {
        background: #ff5733;
        opacity: 0.5;
    }

    .urgent-projects-swiper .swiper-pagination-bullet-active {
        background: #ff5733;
        opacity: 1;
    }

    /* تنسيقات الزر النشط */
    .active {
        border: 2px solid white;
        background-color: #c3875d;
        color: white;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
        transform: scale(1.05);
    }

    /* تنسيقات العناصر */
    label {
        margin: 15px 0 15px;
    }

    /* تعديلات للشاشات الصغيرة */
    @media (max-width: 427px) {
        #donation_amount {
            top: calc(40% - 45px) !important;
        }
        #number_shares {
                top: calc(50% - 125px);
        }
    }

    /* تنسيقات المشاريع العاجلة */
    .urgent-project {
        position: relative;
        overflow: hidden;
        transform: translateY(0);
        transition: all 0.4s ease;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        background: linear-gradient(to bottom, #fff, #fff8f6);
        min-width: 80%;
        max-width: 90%;
        width: 100%;
        flex: 0 0 auto;
        border-radius: 16px;
        display: flex;
        flex-direction: row;
    }


    @media (max-width: 767px) {
        .urgent-project {
            flex-direction: column;
        }

        .urgent-projects-swiper .swiper-slide {
            display: block !important;
        }
    }

    .urgent-badge {
        position: absolute;
        top: 0;
        right: 0;
        background-color: #ff5733;
        color: white;
        padding: 8px 15px;
        font-weight: bold;
        font-size: 0.9rem;
        border-bottom-left-radius: 12px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        z-index: 10;
    }

    @keyframes badgePulse {
        0% {
            background-color: #ff5733;
        }

        50% {
            background-color: #ff3300;
        }

        100% {
            background-color: #ff5733;
        }
    }

    .urgent-project .project-image {
        position: relative;
        overflow: hidden;
        flex: 0 0 40%;
    }

    @media (min-width: 768px) {
        .urgent-project .project-image {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }
    }

    @media (max-width: 767px) {
        .urgent-project .project-image {
            border-top-left-radius: 14px;
            border-top-right-radius: 14px;
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
        }

        .urgent-progress-bar {
            margin-top: 40px;
        }
    }

    .urgent-project .project-image img {
        width: 100%;
        height: auto;
        max-height: 300px;
        object-fit: contain;
        /* أو remove تمامًا */
    }


    .urgent-project .project-image::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(278deg, rgba(255, 87, 51, 0.3), transparent);
        pointer-events: none;
    }

    .urgent-project .project-details {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .urgent-section {
        margin-bottom: 2.5rem;
        padding: 1.5rem;
        background-color: #fff8f6;
        border-radius: 16px;
        border-right: 5px solid #ff5733;
        box-shadow: 0 5px 15px rgba(255, 87, 51, 0.1);
    }

    .urgent-section-title {
        position: relative;
        display: inline-block;
        margin-bottom: 1rem;
    }

    .urgent-section-title::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 0;
        width: 100%;
        height: 3px;
        background: linear-gradient(to right, #ff5733, transparent);
    }

    .pulse-animation {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(255, 87, 51, 0.7);
        }

        70% {
            box-shadow: 0 0 0 10px rgba(255, 87, 51, 0);
        }

        100% {
            box-shadow: 0 0 0 0 rgba(255, 87, 51, 0);
        }
    }

    .urgent-progress-bar {
        background-color: #ffded7 !important;
    }

    .urgent-progress-fill {
        background-color: #ff5733 !important;
    }

    .urgent-progress-indicator {
        background-color: #ff5733 !important;
    }

    .urgent-project .project-details h3 {
        color: #ff3300;
        font-weight: 700;
    }

    .urgent-slider {
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
        padding: 10px 5px;
    }

    .urgent-slider::-webkit-scrollbar {
        height: 8px;
    }

    .urgent-slider::-webkit-scrollbar-track {
        background: #ffded7;
        border-radius: 10px;
    }

    .urgent-slider::-webkit-scrollbar-thumb {
        background: #ff5733;
        border-radius: 10px;
    }

    .urgent-slider::-webkit-scrollbar-thumb:hover {
        background: #ff3300;
    }

    /* تأثير الظل على جوانب السلايدر */
    .overflow-x-auto {
        position: relative;
    }

    .overflow-x-auto::before,
    .overflow-x-auto::after {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        width: 30px;
        z-index: 2;
        pointer-events: none;
    }

    .overflow-x-auto::before {
        left: 0;
        background: linear-gradient(to right, rgba(255, 248, 246, 1), rgba(255, 248, 246, 0));
    }

    .overflow-x-auto::after {
        right: 0;
        background: linear-gradient(to left, rgba(255, 248, 246, 1), rgba(255, 248, 246, 0));
    }
</style>

<!-- قسم المشاريع -->
<div id="projects-section" class="projects-section p-5">
    <h2 class="text-2xl font-semibold mb-6 text-center">مشاريعنا</h2>

    <!-- قسم المشاريع العاجلة -->
    <div id="urgent-projects-section" class="urgent-section mb-8 rtl w-full" style="display: none;">
        <div class="flex items-center justify-between mb-6">
            <div class="flex items-center gap-3">
                <h3 class="text-2xl font-bold text-red-600 urgent-section-title">مشاريع عاجلة</h3>
                <span class="inline-block w-4 h-4 bg-red-500 rounded-full pulse-animation"></span>
            </div>
            <div class="hidden md:block">
                <!--<div class="bg-white px-4 py-2 rounded-lg shadow-sm border border-red-200 flex items-center gap-2">-->
                <!--    <span class="text-red-600">⚠️</span>-->
                <!--    <span class="text-gray-700 text-sm">أهل غزه يحتاجون لمساعدتكم الآن</span>-->
                <!--</div>-->
            </div>
        </div>
        <p class="text-gray-700 mb-6 md:hidden">هذه المشاريع تحتاج إلى دعمكم العاجل</p>

        <!-- حاوية المشاريع العاجلة باستخدام Swiper JS -->
        <div id="urgent-projects-container" class="w-full pb-4">
            <!-- Swiper -->
            <div class="swiper urgent-projects-swiper">
                <div class="swiper-wrapper">
                    <!-- المشاريع العاجلة ستظهر هنا كشرائح Swiper -->
                </div>
                <!-- إضافة نقاط التنقل -->
                <div class="swiper-pagination"></div>
            </div>
        </div>
    </div>

    <!-- أزرار تصنيف المشاريع -->
    <div class="flex justify-center gap-4 mb-6">
        <button id="renewed-btn" onclick="loadProjects('renewed')" class="btn px-6 py-2 rounded-lg">مشاريع متجددة</button>
        <button id="always-btn" onclick="loadProjects('always')" class="btn px-6 py-2 rounded-lg">مشاريع دائمة</button>
    </div>

    <!-- حاوية المشاريع العادية - سيتم ملؤها ديناميكيًا بواسطة جافاسكريبت -->
    <div id="projects-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 rtl">
    </div>
</div>

<!-- سكريبت إدارة المشاريع -->
<script defer>
    // ثوابت وإعدادات - جلب أسعار الأسهم من PHP
    const SHARE_PRICES = <?php echo json_encode($sharePrices); ?>;

    // تحميل المشاريع حسب النوع
    function loadProjects(type) {
        // تحديث حالة الأزرار
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(button => button.classList.remove('active'));

        let activeButton = document.getElementById(`${type}-btn`);
        activeButton.classList.add('active');

        // جلب المشاريع من الخادم
        const xhr = new XMLHttpRequest();
        xhr.open('GET', `/projects/api/get_projects.php?type=${type}`, true);

        xhr.onload = function() {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        displayProjects(response.projects);
                    } else {
                        alert(response.message || 'لا توجد مشاريع');
                    }
                } catch (e) {
                    console.error('خطأ في تحويل JSON:', e);
                    alert('حدث خطأ أثناء معالجة البيانات.');
                }
            } else {
                alert('فشل الاتصال بالخادم');
            }
        };

        xhr.onerror = function() {
            alert('حدث خطأ أثناء الاتصال بالخادم.');
        };

        xhr.send();
    }

    // عرض المشاريع في الصفحة
    function displayProjects(projects) {
        const container = document.getElementById('projects-container');

        // تفريغ الحاوية
        container.innerHTML = '';

        // فلترة المشاريع العادية فقط (غير العاجلة)
        const regularProjects = projects.filter(project => project.is_urgent !== 'yes');

        // عرض المشاريع العادية
        regularProjects.forEach(project => {
            const projectElement = createProjectElement(project, false);
            container.appendChild(projectElement);
        });

        // تحديث المشاريع العاجلة في كل مرة يتم فيها تحميل المشاريع
        loadUrgentProjects();
    }

    // إنشاء عنصر المشروع
    function createProjectElement(project, isUrgent) {
        // إنشاء عنصر المشروع
        const projectElement = document.createElement('div');

        if (isUrgent) {
            // تنسيقات خاصة للمشاريع العاجلة
            projectElement.classList.add('project-item', 'urgent-project', 'flex', 'flex-col', 'justify-between', 'relative');

            // إضافة شارة "عاجل"
            const urgentBadge = document.createElement('div');
            urgentBadge.classList.add('urgent-badge');
            urgentBadge.textContent = 'عاجل';
            projectElement.appendChild(urgentBadge);

            // // إضافة أيقونة نبض للمشاريع العاجلة
            // const pulseIcon = document.createElement('div');
            // pulseIcon.classList.add('absolute', 'bottom-2', 'left-5', 'z-10');
            // pulseIcon.innerHTML = '<span class="inline-block w-3 h-3 bg-red-500 rounded-full pulse-animation"></span>';
            // projectElement.appendChild(pulseIcon);
        } else {
            // تنسيقات للمشاريع العادية
            projectElement.classList.add('project-item', 'flex', 'flex-col', 'justify-between', 'bg-white', 'shadow-lg', 'rounded-lg', 'p-4', 'relative');
        }

        // حساب تاريخ انتهاء المشروع
        const currentDate = new Date();
        const endDate = new Date(project.created_at);
        endDate.setDate(endDate.getDate() + project.duration_days);

        // إنشاء محتوى المشروع
        projectElement.innerHTML += createProjectHTML(project, isUrgent);

        // إضافة زر التبرع
        const donateButtonContainer = createDonateButton(project, isUrgent);
        projectElement.appendChild(donateButtonContainer);

        // إضافة مستمع الحدث لزر التبرع
        setupDonateButtonListener(donateButtonContainer, project);

        return projectElement;
    }

    // إنشاء HTML للمشروع
    function createProjectHTML(project, isUrgent) {
        // تنسيق الأرقام للعرض
        const formatNumber = (num) => {
            if (num >= 1e6) return `${(num/1e6).toFixed(1).replace(/\.0$/, '')}M`;
            else if (num >= 1e3) return `${(num/1e3).toFixed(1).replace(/\.0$/, '')}K`;
            else return num.toFixed(0);
        };

        // حساب نسبة التبرع الحالية
        const current = parseFloat(project.current_donations.toString().replace(/,/g, ''));
        const remaining = parseFloat(project.remaining_amount.toString().replace(/,/g, ''));
        const donationText = current >= remaining ? 'مكتملة' : `${formatNumber(remaining)} / ${formatNumber(current)}`;
        const progressPercentage = Math.min(parseFloat(project.current_donations) / parseFloat(project.remaining_amount), 1) * 100;

        // تحديد ما إذا كان المشروع عاجلاً إذا لم يتم تمريره
        isUrgent = isUrgent || project.is_urgent === 'yes';

        // تحديد فئات CSS للتقدم بناءً على ما إذا كان المشروع عاجلاً
        const progressBarClass = isUrgent ? 'urgent-progress-bar' : 'bg-gray-200';
        const progressFillClass = isUrgent ? 'urgent-progress-fill' : '';
        const progressIndicatorClass = isUrgent ? 'urgent-progress-indicator' : '';
        const progressFillColor = isUrgent ? '#ff5733' : '#c3875d';

        // تحديد طول الوصف بناءً على ما إذا كان المشروع عاجلاً
        const description = isUrgent ?
            project.description.length > 150 ?
            project.description.substring(0, 150) + '...' :
            project.description :
            project.description;

        // تنسيقات إضافية للمشاريع العاجلة
        const titleClass = isUrgent ? 'text-xl font-bold text-red-600' : 'text-xl font-semibold';
        const imageClass = isUrgent ?
            'w-full h-full object-cover' :
            'w-full object-cover h-50 rounded-t-lg';

        if (isUrgent) {
            // تصميم أفقي للمشاريع العاجلة
            return `
                <div class="project-image">
                    <img src="../assets/img/project/${project.image_path}" alt="${project.title}" class="${imageClass}">
                </div>
                <div class="project-details p-4 rtl flex-1">
                    <div>
                        <h3 class="${titleClass} mb-2">${project.title}</h3>
                        <p class="text-gray-600 mb-4">${description}</p>
                    </div>

                    <div class="${progressBarClass} rounded-full w-full h-2.5 max-w-4xl mx-auto mt-2">
                        <div class="h-full rounded-full relative rtl ${progressFillClass}"
                             style="width: ${progressPercentage}%; background-color: ${progressFillColor};">
                            <div class="absolute text-xs text-white font-bold px-1.5 ${progressIndicatorClass}"
                                 style="background-color: ${progressFillColor}; min-width: max-content; min-height: 24px; top: -40px; left: 0%; transform: translateX(-50%); border-radius: 9999px; display: flex; align-items: center; justify-content: center;">
                                ${donationText}
                                <div style="position: absolute; bottom: -8px; left: 50%; transform: translateX(-50%); width: 0; height: 0; border-left: 5px solid transparent; border-right: 5px solid transparent; border-top: 5px solid ${progressFillColor};"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } else {
            // تصميم عمودي للمشاريع العادية
            return `
                <div class="project-image">
                    <img src="../assets/img/project/${project.image_path}" alt="${project.title}" class="${imageClass}">
                </div>
                <div class="project-details p-4 rtl">
                    <h3 class="${titleClass} mb-2">${project.title}</h3>
                    <p class="text-gray-600 mb-14">${description}</p>

                    <div class="${progressBarClass} rounded-full w-full h-2.5 max-w-4xl mx-auto mt-10">
                        <div class="h-full rounded-full relative rtl ${progressFillClass}"
                             style="width: ${progressPercentage}%; background-color: ${progressFillColor};">
                            <div class="absolute text-xs text-white font-bold px-1.5 ${progressIndicatorClass}"
                                 style="background-color: ${progressFillColor}; min-width: max-content; min-height: 24px; top: -40px; left: 0%; transform: translateX(-50%); border-radius: 9999px; display: flex; align-items: center; justify-content: center;">
                                ${donationText}
                                <div style="position: absolute; bottom: -8px; left: 50%; transform: translateX(-50%); width: 0; height: 0; border-left: 5px solid transparent; border-right: 5px solid transparent; border-top: 5px solid ${progressFillColor};"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    // إنشاء زر التبرع
    function createDonateButton(project, isUrgent) {
        const donateButtonContainer = document.createElement('div');

        if (isUrgent) {
            // تنسيق خاص للمشاريع العاجلة
            donateButtonContainer.classList.add('project-donate', 'p-4', 'flex', 'justify-end');
        } else {
            // تنسيق للمشاريع العادية
            donateButtonContainer.classList.add('project-donate', 'p-4', 'flex', 'justify-between');
        }

        const buttonText = project.project_kafel === 'yes' ? 'أكفلني' : 'تبرع الآن';

        // تحديد لون الزر (أحمر للمشاريع العاجلة، اللون الافتراضي للمشاريع العادية)
        const buttonColor = isUrgent ? '#ff5733' : '#c3875d';

        // إضافة تأثيرات إضافية للمشاريع العاجلة
        const urgentClasses = isUrgent ? 'hover:bg-red-700 transition-all duration-300' : '';

        // تحديد عرض الزر بناءً على نوع المشروع
        const buttonWidth = isUrgent ? 'w-auto' : 'w-full';

        donateButtonContainer.innerHTML = `
            <button id="donate_now" style="background-color: ${buttonColor}; color:#fff;"
                    data-project-id="${project.id}"
                    data-project-title="${project.title}"
                    data-project-kafel="${project.project_kafel}"
                    data-project-urgent="${isUrgent ? 'yes' : 'no'}"
                    class="btn ${buttonWidth} px-6 py-2 font-semibold rounded-lg ${urgentClasses}">
                ${isUrgent ? buttonText : buttonText}
            </button>
        `;

        return donateButtonContainer;
    }

    // إعداد مستمع الحدث لزر التبرع
    function setupDonateButtonListener(container, project) {
        const donateButton = container.querySelector('button');

        donateButton.addEventListener('click', function() {
            const projectId = donateButton.getAttribute('data-project-id');
            const projectTitle = donateButton.getAttribute('data-project-title');
            const projectKafel = donateButton.getAttribute('data-project-kafel');

            showDonationForm(projectId, projectTitle, projectKafel);
        });
    }

    // عرض نموذج التبرع
    function showDonationForm(projectId, projectTitle, projectKafel) {
        Swal.fire({
            title: '<span style="color: #10b981;">إرفاق فاتورة الدفع</span>',
            width: '100%',
            html: createDonationFormHTML(projectTitle, projectKafel),
            showCancelButton: true,
            confirmButtonText: 'إرسال',
            confirmButtonColor: '#10b981',
            cancelButtonText: 'إلغاء',
            preConfirm: () => validateAndCollectFormData(projectId, projectTitle, projectKafel)
        }).then((result) => {
            if (result.isConfirmed) {
                submitDonationForm(result.value, projectId, projectTitle);
            }
        });

        // إضافة مستمعي الأحداث للنموذج
        setupFormEventListeners();
    }

    // إنشاء HTML لنموذج التبرع
    function createDonationFormHTML(projectTitle, projectKafel) {
        let kafelFields = '';

        if (projectKafel === 'yes') {
            kafelFields = `
                <div class="mb-4">
                    <label for="kafelType" class="block text-right text-md font-medium text-gray-700">نوع الكفالة</label>
                    <select id="kafelType" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" required>
                        <option value="" disabled selected hidden>أختر نوع الكفالة</option>
                        <option value="child">كفالة طفل</option>
                        <option value="family">كفالة أسرة</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label for="kafelFrequency" class="block text-right text-md font-medium text-gray-700">تكرار الكفالة</label>
                    <select id="kafelFrequency" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" required>
                        <option value="" disabled selected hidden>تكرار الكفالة</option>
                        <option value="one-time">لمرة واحدة</option>
                        <option value="monthly">شهرية</option>
                    </select>
                </div>
            `;
        }

        return `

        <!-- ملاحظة مهمة للمتبرعين -->
<div dir="rtl" class="bg-yellow-50 text-right border-r-4 border-yellow-500 p-4 rounded-lg shadow-sm mb-6">
  <div class="flex items-start">
    <div class="flex-shrink-0">
      <svg class="h-5 w-5 text-yellow-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856
                 c1.54 0 2.502-1.667 1.732-3L13.732 4
                 c-.77-1.333-2.694-1.333-3.464 0L3.34 16
                 c-.77 1.333.192 3 1.732 3z" />
      </svg>
    </div>
    <div class="mr-3">
      <h3 class="text-md font-bold text-yellow-800">ملاحظة مهمة</h3>
      <div class="mt-1 text-sm text-yellow-700">
        <p>يرجى التأكد من ملء جميع البيانات المطلوبة بشكل صحيح وبيانات حقيقية وإرفاق صورة فاتورة التبرع أو إيصال الدفع حتى يتم قبول تبرعكم.</p>
      </div>
    </div>
  </div>
</div>

            <form id="donationForm" enctype="multipart/form-data">
                <div class="mb-4">
                    <label for="name" class="block text-right text-md font-medium text-gray-700">اسم المشروع</label>
                    <input type="text" placeholder="${projectTitle}" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" disabled />
                </div>

                ${kafelFields}

                <div class="mb-4">
                    <label for="name" class="block text-right text-md font-medium text-gray-700">الاسم</label>
                    <input type="text" id="name" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" placeholder="أدخل الاسم" required>
                </div>

                <div class="mb-4">
                    <label for="country" class="block text-right text-md font-medium text-gray-700">البلد</label>
                    <input type="text" id="country" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" placeholder="أدخل البلد" required>
                </div>

                <div class="mb-4">
                    <label for="whatsapp" class="block text-right text-md font-medium text-gray-700">واتساب للتواصل</label>
                    <input type="text" id="whatsapp" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" placeholder="أدخل رقم الواتساب" required>
                </div>

                <div class="mb-4">
                    <label for="email" class="block text-right text-md font-medium text-gray-700">البريد الإلكتروني</label>
                    <input type="email" id="email" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" placeholder="أدخل البريد الإلكتروني" required>
                </div>

                <div class="mb-4">
                    <label for="currency" class="block text-right text-md font-medium text-gray-700">العملة</label>
                    <select id="currency" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" required>
                        <option value="" disabled selected hidden>أختر العملة</option>
                        <?php foreach ($currencies as $currency): ?>
                            <option value="<?php echo $currency['symbol']; ?>"><?php echo $currency['name_ar']; ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="mb-4 relative">
                    <label for="donation_amount" class="block text-right text-md font-medium text-gray-700">قيمة التبرع</label>
                    <input type="number" id="donation_amount" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm pl-16" placeholder="أدخل قيمة التبرع" min="0" required>
                    <input type="hidden" id="number_shares_value" name="number_shares_value">
                    <span id="number_shares" class="absolute bg-green-600 rtl text-white top-1/2 left-4 transform -translate-y-1/2 text-md px-2" style="top: calc(50% - 75px);">
                        0
                    </span>
<div class="Share_price py-3">
<?php foreach ($currencies as $currency): ?>
    <?php
    // تنسيق القيمة: إذا كانت القيمة رقماً صحيحاً، نعرضها بدون كسور عشرية
    $value = $currency['value'];
    $formattedValue = (floor($value) == $value) ? floor($value) : $value;

    // تحديد نص العملة المناسب
    $currencyText = '';
    switch ($currency['symbol']) {
        case 'USD':
            $currencyText = 'بالدولار';
            break;
        case 'EUR':
            $currencyText = 'بالـيورو';
            break;
        case 'EGP':
            $currencyText = 'بالجنيه المصري';
            break;
        case 'JOD':
            $currencyText = 'بالدينار الأردني';
            break;
        default:
            $currencyText = 'بالـ' . $currency['name_ar'];
    }
    ?>
    <p class="rtl text-green-600" data-currency="<?php echo $currency['symbol']; ?>" data-price="<?php echo $currency['value']; ?>">
        سعر السهم <?php echo $currencyText; ?> = <?php echo $formattedValue; ?> <?php echo $currency['name_ar']; ?>
    </p>
<?php endforeach; ?>
</div>
                </div>

                <div class="mb-4">
                    <label for="paymentMethod" class="block text-right text-md font-medium text-gray-700">وسيلة الدفع</label>
                    <select onChange="handlePaymentMethodChange()" id="paymentMethod" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" required>
                        <option value="" disabled selected hidden>أختر وسيلة الدفع</option>
                        <?php
                        foreach ($paymentMethods as $method) {
                            echo "<option value=\"$method\">" . htmlspecialchars($method) . "</option>";
                        }
                        ?>
                    </select>
                </div>

                <div id="payment_method"></div>

                <div class="mb-4">
                    <label for="purpose" class="block text-right text-md font-medium text-gray-700">غرض التبرع</label>
                    <textarea id="purpose" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" placeholder="غرض التبرع" rows="3"></textarea>
                </div>

                <div class="mb-4">
                    <label for="proofImage" class="block text-right text-md font-medium text-gray-700 rtl">
                        يمكنك تحديد ورفع أكثر من صورة أو ملف PDF كمستند إثبات الدفع
                    </label>
                    <input type="file" id="proofImage" name="proofImages[]" multiple class="rtl file-input file-input-bordered mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" required>
                </div>
            </form>
        `;
    }

    // إعداد مستمعي الأحداث للنموذج
    function setupFormEventListeners() {
        // مستمع لحقل مبلغ التبرع
        document.getElementById('donation_amount').addEventListener('input', function(event) {
            let value = parseFloat(event.target.value) || 0;
            if (value < 0) event.target.value = 0;
            calculateDonation();
        });

        // مستمع لتغيير العملة
        document.getElementById('currency').addEventListener('change', calculateDonation);
    }

    // حساب عدد الأسهم بناءً على مبلغ التبرع والعملة
    function calculateDonation() {
        const currency = document.getElementById('currency').value;
        const donationAmount = parseFloat(document.getElementById('donation_amount').value) || 0;
        const numberSharesElement = document.getElementById('number_shares');
        const numberSharesValueElement = document.getElementById('number_shares_value');

        if (!currency) {
            numberSharesElement.textContent = "يرجى اختيار العملة أولاً";
            numberSharesValueElement.value = "";
            return;
        }

        if (donationAmount > 0) {
            const shares = donationAmount / SHARE_PRICES[currency];

            // تنسيق الرقم: إذا كان رقماً صحيحاً، نعرضه بدون كسور عشرية
            let formattedShares;
            if (Math.floor(shares) === shares) {
                formattedShares = Math.floor(shares).toString();
            } else {
                formattedShares = shares.toFixed(2);
                // إزالة الأصفار الزائدة في نهاية الرقم العشري
                formattedShares = formattedShares.replace(/\.?0+$/, '');
            }

            numberSharesElement.textContent = formattedShares;
            numberSharesValueElement.value = formattedShares;
        } else {
            numberSharesElement.textContent = "0";
            numberSharesValueElement.value = "0";
        }
    }

    // التحقق من صحة البيانات وجمعها
    function validateAndCollectFormData(projectId, projectTitle, projectKafel) {
        const name = document.getElementById('name').value;
        const country = document.getElementById('country').value;
        const whatsapp = document.getElementById('whatsapp').value;
        const paymentMethod = document.getElementById('paymentMethod').value;
        const donation_amount = document.getElementById('donation_amount').value;
        const purpose = document.getElementById('purpose').value;
        const currency = document.getElementById('currency').value;
        const email = document.getElementById('email').value;
        const number_shares = document.getElementById('number_shares').textContent;

        const kafelTypeElement = document.getElementById('kafelType');
        const kafelFrequencyElement = document.getElementById('kafelFrequency');
        const kafelType = kafelTypeElement ? kafelTypeElement.value : null;
        const kafelFrequency = kafelFrequencyElement ? kafelFrequencyElement.value : null;

        const proofImageInput = document.getElementById('proofImage');
        const files = proofImageInput.files;

        // التحقق من وجود ملفات مرفقة
        if (files.length === 0) {
            Swal.showValidationMessage('يرجى رفع صورة واحدة على الأقل');
            return;
        }

        // التحقق من تعبئة جميع الحقول المطلوبة
        if (!country || !whatsapp || !email || !number_shares || !paymentMethod || !proofImageInput || !currency ||
            (projectKafel === 'yes' && (!kafelType || !kafelFrequency))) {
            Swal.showValidationMessage(`يرجى تعبئة جميع الحقول المطلوبة`);
        } else if (parseFloat(donation_amount) <= 0) {
            Swal.showValidationMessage(`مبلغ التبرع يجب أن يكون أكبر من الصفر`);
        } else {
            return {
                name,
                country,
                whatsapp,
                email,
                paymentMethod,
                number_shares,
                purpose,
                proofImage: proofImageInput,
                currency,
                kafelType,
                kafelFrequency,
                projectId,
                projectTitle,
                donationAmount: parseFloat(donation_amount)
            };
        }
    }

    // إرسال نموذج التبرع
    function submitDonationForm(formData, projectId, projectTitle) {
        const formDataObj = new FormData();

        // إضافة الملفات
        Array.from(formData.proofImage.files).forEach(file => {
            formDataObj.append('proofImages[]', file);
        });

        // إضافة بقية البيانات
        formDataObj.append('name', formData.name);
        formDataObj.append('country', formData.country);
        formDataObj.append('whatsapp', formData.whatsapp);
        formDataObj.append('paymentMethod', formData.paymentMethod);
        formDataObj.append('number_shares', formData.number_shares);
        formDataObj.append('purpose', formData.purpose);
        formDataObj.append('currency', formData.currency);
        formDataObj.append('email', formData.email);
        formDataObj.append('kafelType', formData.kafelType);
        formDataObj.append('kafelFrequency', formData.kafelFrequency);
        formDataObj.append('projectId', formData.projectId);
        formDataObj.append('projectTitle', formData.projectTitle);
        formDataObj.append('donationAmount', formData.donationAmount);

        // عرض نافذة التحميل
        Swal.fire({
            title: 'جاري المعالجة...',
            html: '<div class="flex flex-col items-center"><span class="loading loading-spinner loading-lg text-green-600 mb-4"></span></div>',
            showConfirmButton: false,
            allowOutsideClick: false
        });

        // إرسال البيانات إلى الخادم
        fetch('/assets/api/save_donations.php', {
                method: 'POST',
                body: formDataObj
            })
            .then(response => response.json())
            .then(data => {
                Swal.close(); // إغلاق نافذة التحميل
                if (data.success) {
                    Swal.fire('تم الإرسال بنجاح!', 'شكرًا لك على تبرعك!', 'success');
                } else {
                    Swal.fire('حدث خطأ!', data.message, 'error');
                }
            })
            .catch(error => {
                Swal.close(); // إغلاق نافذة التحميل
                Swal.fire('حدث خطأ!', 'يرجى المحاولة مرة أخرى.', 'error');
            });
    }

    // جلب عناوين الدفع حسب الطريقة المختارة
    function handlePaymentMethodChange() {
        const paymentMethod = document.getElementById('paymentMethod').value;
        console.log('Payment method changed to:', paymentMethod);

        if (!paymentMethod) {
            document.getElementById('payment_method').innerHTML = '';
            return;
        }

        fetchPaymentAddresses(paymentMethod);
    }

    // جلب عناوين الدفع من الخادم
    async function fetchPaymentAddresses(method) {
        try {
            const response = await fetch('/assets/api/get-wallet-addresses.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    method: method
                })
            });

            const data = await response.json();

            if (data.success) {
                displayPaymentAddresses(data.addresses);
            } else {
                document.getElementById('payment_method').innerHTML = '<p class="text-red-500">لا توجد عناوين متاحة لهذه الوسيلة.</p>';
            }
        } catch (error) {
            console.error('حدث خطأ أثناء جلب العناوين:', error);
            document.getElementById('payment_method').innerHTML = '<p class="text-red-500">حدث خطأ في الاتصال بالخادم.</p>';
        }
    }

    // عرض عناوين الدفع
    function displayPaymentAddresses(addresses) {
        let addressListHtml = `
            <h3 class="text-lg font-semibold mb-3 text-gray-800">تفضل بالتبرع على العناوين التالية</h3>
            <ul class="space-y-2">
        `;

        // عرض العناوين كعناصر منفصلة
        addresses.forEach(address => {
            // إزالة أي تاغات HTML محتملة والتحقق من النص الفعلي
            const addressText = address.replace(/<[^>]*>/g, '');

            // التحقق إذا كان العنوان رابطًا
            if (addressText.startsWith('http://') || addressText.startsWith('https://')) {
                addressListHtml += `
                    <li class="p-2 bg-gray-100 rounded-lg shadow-sm hover:bg-gray-200">
                        <a href="${addressText}" class="text-green-500 hover:text-green-800" target="_blank">اضغط هنا للتبرع</a>
                    </li>
                `;
            } else {
                addressListHtml += `
                    <li class="p-2 bg-gray-100 rounded-lg shadow-sm hover:bg-gray-200">
                        <span class="text-green-500">${addressText}</span>
                    </li>
                `;
            }
        });

        addressListHtml += '</ul>';
        document.getElementById('payment_method').innerHTML = addressListHtml;
    }

    // تحميل المشاريع العاجلة باستخدام Swiper JS
    function loadUrgentProjects() {
        // console.log('جاري تحميل المشاريع العاجلة...');

        // جلب المشاريع العاجلة من الخادم
        fetch('/projects/api/get_urgent_projects.php')
            .then(response => response.json())
            .then(data => {
                const urgentSection = document.getElementById('urgent-projects-section');
                const urgentContainer = document.getElementById('urgent-projects-container');
                const swiperWrapper = urgentContainer.querySelector('.swiper-wrapper');

                if (data.success && data.projects && data.projects.length > 0) {
                    // console.log(`تم العثور على ${data.projects.length} مشروع عاجل`);

                    // تفريغ الحاوية
                    swiperWrapper.innerHTML = '';

                    // إظهار قسم المشاريع العاجلة
                    urgentSection.style.display = 'block';

                    // عرض المشاريع العاجلة
                    data.projects.forEach(project => {
                        // إنشاء عنصر شريحة Swiper
                        const slideElement = document.createElement('div');
                        slideElement.className = 'swiper-slide';

                        // إنشاء عنصر المشروع
                        const projectElement = createProjectElement(project, true);

                        // إضافة المشروع إلى الشريحة
                        slideElement.appendChild(projectElement);

                        // إضافة الشريحة إلى حاوية Swiper
                        swiperWrapper.appendChild(slideElement);
                    });

                    // التأكد من أن قسم المشاريع العاجلة يظهر قبل المشاريع العادية
                    const projectsSection = document.getElementById('projects-section');
                    const regularProjectsContainer = document.getElementById('projects-container');

                    // إعادة ترتيب العناصر إذا لزم الأمر
                    if (projectsSection.firstElementChild !== urgentSection) {
                        projectsSection.insertBefore(urgentSection, regularProjectsContainer.previousElementSibling);
                    }

                    // تهيئة Swiper
                    initUrgentProjectsSwiper(data.projects);
                } else {
                    // console.log('لا توجد مشاريع عاجلة');
                    // إخفاء قسم المشاريع العاجلة إذا لم تكن هناك مشاريع عاجلة
                    urgentSection.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل المشاريع العاجلة:', error);
                // إخفاء قسم المشاريع العاجلة في حالة حدوث خطأ
                document.getElementById('urgent-projects-section').style.display = 'none';
            });
    }

    // تهيئة Swiper للمشاريع العاجلة
    function initUrgentProjectsSwiper(projects) {
        // تدمير أي نسخة سابقة من Swiper
        if (window.urgentProjectsSwiper) {
            window.urgentProjectsSwiper.destroy(true, true);
        }

        // تهيئة Swiper جديد
        window.urgentProjectsSwiper = new Swiper('.urgent-projects-swiper', {
            slidesPerView: 'auto',
            spaceBetween: 20,
            centeredSlides: true,
            loop: projects && projects.length > 1,
            // autoplay: {
            //     delay: 5000,
            //     disableOnInteraction: false,
            // },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            breakpoints: {
                // عندما يكون عرض النافذة >= 320px
                320: {
                    slidesPerView: 1,
                    spaceBetween: 10
                },
                // عندما يكون عرض النافذة >= 768px
                768: {
                    slidesPerView: 1,
                    spaceBetween: 20
                },
                // عندما يكون عرض النافذة >= 1024px
                1024: {
                    slidesPerView: 1,
                    spaceBetween: 30
                }
            }
        });
    }



    // تحميل المشاريع عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        // تحميل المشاريع العاجلة أولاً
        loadUrgentProjects();

        // تأخير تحميل المشاريع العادية لضمان ظهور المشاريع العاجلة أولاً
        setTimeout(function() {
            // تحميل المشاريع الدائمة كافتراضية
            loadProjects('always');
        }, 500);

        // إضافة مستمعي الأحداث لأزرار تصنيف المشاريع
        document.querySelectorAll('.btn').forEach(button => {
            button.addEventListener('click', function() {
                // التأكد من أن قسم المشاريع العاجلة يظل ظاهراً بعد النقر على أزرار التصنيف
                setTimeout(loadUrgentProjects, 500);
            });
        });

        // إضافة مستمع لتغيير حجم النافذة لضمان التجاوب
        window.addEventListener('resize', function() {
            // تحديث تنسيقات المشاريع العاجلة عند تغيير حجم النافذة
            const urgentContainer = document.getElementById('urgent-projects-container');
            if (urgentContainer) {
                const sliderContainer = urgentContainer.querySelector('.urgent-slider');
                if (sliderContainer) {
                    const urgentProjects = sliderContainer.querySelectorAll('.urgent-project');
                    urgentProjects.forEach(project => {
                        // تأكد من أن المشروع يأخذ العرض المناسب
                        if (window.innerWidth < 768) {
                            // للشاشات الصغيرة
                            project.style.minWidth = '90%';
                            project.style.maxWidth = '95%';
                            project.style.flexDirection = 'column';
                        } else {
                            // للشاشات الكبيرة
                            project.style.minWidth = '80%';
                            project.style.maxWidth = '90%';
                            project.style.flexDirection = 'row';
                        }
                    });
                }
            }
        });
    });
</script>