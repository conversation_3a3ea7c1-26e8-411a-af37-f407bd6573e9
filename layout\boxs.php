<?php
require($_SERVER['DOCUMENT_ROOT'] . '/config/conn.php');

$sharesQuery = "SELECT SUM(number_shares) AS total_shares FROM donations";
$sharesResult = mysqli_query($conn, $sharesQuery);
$totalShares = 0;

if ($sharesResult && mysqli_num_rows($sharesResult) > 0) {
    $sharesData = mysqli_fetch_assoc($sharesResult);
    $totalShares = $sharesData['total_shares'];

    if (is_null($totalShares) || $totalShares === '') {
        $totalShares = 0;
    }
}

$settingsQuery = "SELECT beneficiary_family FROM settings LIMIT 1";
$settingsResult = mysqli_query($conn, $settingsQuery);
$beneficiaryFamily = 0;

if ($settingsResult && mysqli_num_rows($settingsResult) > 0) {
    $settingsData = mysqli_fetch_assoc($settingsResult);
    $beneficiaryFamily = $settingsData['beneficiary_family'];

    if (is_null($beneficiaryFamily) || $beneficiaryFamily === '') {
        $beneficiaryFamily = 0;
    }
}
?>

<div class="bg-gray-100 p-6">
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-6">

        <!-- Box 1: إجمالي الأسهم -->
        <div class="bg-white shadow-xl rounded-lg p-6 flex flex-row-reverse justify-between items-center rtl">
            <div class="flex items-center justify-center mb-4">

                <i class="fas fa-chart-line text-4xl" style="color:#c3875d"></i>
            </div>
            <div>
                <h2 class="text-xl font-semibold text-gray-700 mb-2">إجمالي الأسهم حتى الآن</h2>
                <p class="text-2xl font-bold" style="color:#c3875d"><?php echo number_format((float)$totalShares); ?> سهم</p>
            </div>
        </div>

        <!-- Box 2: عدد الأسر المستفيدة -->
        <div class="bg-white shadow-xl rounded-lg p-6 flex flex-row-reverse justify-between items-center rtl">
            <div class="flex items-center justify-center mb-4">

                <i class="fas fa-users text-4xl" style="color:#c3875d"></i>
            </div>
            <div>
                <h2 class="text-xl font-semibold text-gray-700 mb-2">عدد الأسر المستفيدة حتى الآن</h2>
                <p class="text-2xl font-bold" style="color:#c3875d"><?php echo number_format((float)$beneficiaryFamily); ?> أسرة</p>
            </div>
        </div>

    </div>
</div>