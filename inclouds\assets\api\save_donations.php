<?php
include('../../config/conn.php');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من وجود جميع الحقول المطلوبة
    $requiredFields = [
        'name', 'country', 'whatsapp', 'paymentMethod',
        'currency', 'email', 'projectId', 'projectTitle'
    ];
    
    foreach ($requiredFields as $field) {
        if (!isset($_POST[$field])) {
            echo json_encode(['success' => false, 'message' => 'حقل مطلوب مفقود: ' . $field]);
            exit;
        }
    }

    // تنظيف البيانات مع قيم افتراضية
    $name = htmlspecialchars(trim($_POST['name']));
    $country = htmlspecialchars(trim($_POST['country']));
    $whatsapp = htmlspecialchars(trim($_POST['whatsapp']));
    $paymentMethod = htmlspecialchars(trim($_POST['paymentMethod']));
    $number_shares = htmlspecialchars(trim($_POST['number_shares'] ?? '0')); // قيمة افتراضية
    $purpose = htmlspecialchars(trim($_POST['purpose'] ?? ''));
    $currency = htmlspecialchars(trim($_POST['currency']));
    $email = htmlspecialchars(trim($_POST['email']));
    $donationAmount = htmlspecialchars(trim($_POST['donationAmount'] ?? '0')); // قيمة افتراضية

    $kafelType = htmlspecialchars(trim($_POST['kafelType'] ?? ''));
    $kafelFrequency = htmlspecialchars(trim($_POST['kafelFrequency'] ?? ''));

    $projectId = $_POST['projectId'];
    $projectTitle = $_POST['projectTitle'];
    
    // معالجة الملفات المتعددة
    $uploadedFiles = [];
    $targetDir = "../../assets/img/proof/";
    
    // إنشاء المجلد إذا لم يكن موجودًا
    if (!is_dir($targetDir)) {
        mkdir($targetDir, 0777, true);
    }

    // التحقق من وجود ملفات مرفوعة
    if (isset($_FILES['proofImages']) && !empty($_FILES['proofImages']['tmp_name'][0])) {
        foreach ($_FILES['proofImages']['tmp_name'] as $key => $tmpName) {
            if ($_FILES['proofImages']['error'][$key] !== UPLOAD_ERR_OK) {
                echo json_encode(['success' => false, 'message' => 'خطأ في رفع الملف: ' . $_FILES['proofImages']['name'][$key]]);
                exit;
            }

            // التحقق من نوع الملف
            $fileType = strtolower(pathinfo($_FILES['proofImages']['name'][$key], PATHINFO_EXTENSION));
            if (!in_array($fileType, ['jpg', 'jpeg', 'png', 'pdf'])) {
                echo json_encode(['success' => false, 'message' => 'نوع ملف غير مدعوم: ' . $_FILES['proofImages']['name'][$key]]);
                exit;
            }

            // التحقق من حجم الملف
            if ($_FILES['proofImages']['size'][$key] > 5000000) {
                echo json_encode(['success' => false, 'message' => 'حجم الملف كبير جدًا: ' . $_FILES['proofImages']['name'][$key]]);
                exit;
            }

            // إنشاء اسم فريد للملف
            $randomFileName = uniqid('proof_', true) . '.' . $fileType;
            $targetFile = $targetDir . $randomFileName;

            if (move_uploaded_file($tmpName, $targetFile)) {
                $uploadedFiles[] = $randomFileName;
            } else {
                echo json_encode(['success' => false, 'message' => 'فشل في رفع الملف: ' . $_FILES['proofImages']['name'][$key]]);
                exit;
            }
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'لم يتم رفع أي ملفات']);
        exit;
    }

    // تحويل المصفوفة إلى سلسلة
    $proofImagesString = implode(',', $uploadedFiles);

    // إدخال البيانات في قاعدة البيانات
    $stmt = $conn->prepare("INSERT INTO donations (
        name, 
        country, 
        whatsapp, 
        email, 
        payment_method, 
        number_shares, 
        purpose, 
        proof_image, 
        currency, 
        kafel_type, 
        kafel_frequency, 
        project_id, 
        project_title, 
        amount
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $stmt->bind_param('sssssssssssisi', 
        $name,
        $country,
        $whatsapp,
        $email,
        $paymentMethod,
        $number_shares,
        $purpose,
        $proofImagesString,
        $currency,
        $kafelType,
        $kafelFrequency,
        $projectId,
        $projectTitle,
        $donationAmount
    );

    if ($stmt->execute()) {
        // تحديث مبلغ التبرع في المشروع
        $updateStmt = $conn->prepare("UPDATE projects SET current_donations = current_donations + ? WHERE id = ?");
        $updateStmt->bind_param('di', $donationAmount, $projectId);
        
        if ($updateStmt->execute()) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في تحديث مبلغ التبرع']);
        }
        $updateStmt->close();
    } else {
        echo json_encode(['success' => false, 'message' => 'خطأ في إدخال البيانات: ' . $conn->error]);
    }
    
    $stmt->close();
    $conn->close();
} else {
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صالحة']);
}
?>