<?php
session_start();

$_SESSION = array();
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// تدمير الجلسة
session_destroy();

if (isset($_COOKIE['token'])) {
    setcookie('token', '', time() - 3600, '/');
}

// إعادة توجيه المستخدم إلى صفحة تسجيل الدخول
header('Location: ../login.php');
exit();
?>
