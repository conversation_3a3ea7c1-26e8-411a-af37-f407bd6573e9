<?php
require '../../../config/conn.php';

// Check if the user wants Excel or CSV format
$format = isset($_GET['format']) ? $_GET['format'] : 'excel';

// Generate a timestamp for the filename
$timestamp = date('Y-m-d_H-i-s');

if ($format === 'csv') {
    // Set headers for CSV download
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="donations_export_' . $timestamp . '.csv"');

    // Create output stream
    $output = fopen('php://output', 'w');

    // Add UTF-8 BOM to fix Excel encoding issues with Arabic
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
} else {
    // Set headers for Excel download
    header('Content-Type: application/vnd.ms-excel; charset=utf-8');
    header('Content-Disposition: attachment; filename="donations_export_' . $timestamp . '.xls"');

    // Start HTML output for Excel
    echo '<!DOCTYPE html>';
    echo '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">';
    echo '<head>';
    echo '<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">';
    echo '<!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>Donations</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]-->';
    echo '<style>td, th { text-align: right; direction: rtl; font-family: Arial, sans-serif; } th { background-color: #4CAF50; color: white; }</style>';
    echo '</head>';
    echo '<body>';
    echo '<table border="1">';
}

// Define CSV headers (column names)
$headers = [
    'ID',
    'اسم المشروع',
    'الأسم',
    'البلد',
    'واتساب',
    'الايميل',
    'طريقة الدفع',
    'قيمة التبرع',
    'العملة',
    'عدد الأسهم',
    'غرض التبرع',
    'نوع الكفاله',
    'تكرار الكفاله',
    'التاريخ'
];

// Write headers
if ($format === 'csv') {
    // Write headers to CSV
    fputcsv($output, $headers);
} else {
    // Write headers to HTML table
    echo '<tr>';
    foreach ($headers as $header) {
        echo '<th>' . htmlspecialchars($header) . '</th>';
    }
    echo '</tr>';
}

// معلمات الفلتر
$currency = isset($_GET['currency']) ? $_GET['currency'] : '';
$projectType = isset($_GET['project_type']) ? $_GET['project_type'] : '';
$paymentMethod = isset($_GET['payment_method']) ? $_GET['payment_method'] : '';
$dateFrom = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$dateTo = isset($_GET['date_to']) ? $_GET['date_to'] : '';

// بناء شرط WHERE للفلاتر
$whereConditions = [];
$params = [];
$types = '';

if (!empty($currency)) {
    $whereConditions[] = "currency = ?";
    $params[] = $currency;
    $types .= 's';
}

if (!empty($projectType)) {
    if ($projectType === 'urgent') {
        // الحصول على معرفات المشاريع العاجلة
        $projectIdsQuery = "SELECT id FROM projects WHERE is_urgent = 'yes'";
        $stmt = $conn->prepare($projectIdsQuery);
    } else {
        // الحصول على معرفات المشاريع من النوع المحدد
        $projectIdsQuery = "SELECT id FROM projects WHERE project_type = ?";
        $stmt = $conn->prepare($projectIdsQuery);
        $stmt->bind_param('s', $projectType);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    $projectIds = [];
    while ($row = $result->fetch_assoc()) {
        $projectIds[] = $row['id'];
    }
    $stmt->close();

    if (!empty($projectIds)) {
        $projectIdsStr = implode(',', $projectIds);
        $whereConditions[] = "project_id IN ($projectIdsStr)";
    } else {
        // إذا لم يتم العثور على مشاريع من هذا النوع، نضيف شرطًا مستحيلًا لإرجاع نتيجة فارغة
        $whereConditions[] = "1 = 0";
    }
}

if (!empty($paymentMethod)) {
    $whereConditions[] = "payment_method = ?";
    $params[] = $paymentMethod;
    $types .= 's';
}

if (!empty($dateFrom)) {
    $whereConditions[] = "DATE(donation_date) >= ?";
    $params[] = $dateFrom;
    $types .= 's';
}

if (!empty($dateTo)) {
    $whereConditions[] = "DATE(donation_date) <= ?";
    $params[] = $dateTo;
    $types .= 's';
}

// Set a reasonable limit for export (adjust as needed)
$exportLimit = 5000;

// بناء استعلام جلب البيانات مع الفلاتر
$sql = "SELECT * FROM donations";
if (!empty($whereConditions)) {
    $sql .= " WHERE " . implode(' AND ', $whereConditions);
}
$sql .= " ORDER BY id DESC LIMIT " . $exportLimit;

// تنفيذ استعلام جلب البيانات
if (!empty($params)) {
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
} else {
    $result = $conn->query($sql);
}

// Write data to CSV
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        // Format kafel_type
        $kafelType = 'ليس كفالة يتيم';
        if ($row['kafel_type']) {
            $kafelType = match ($row['kafel_type']) {
                'child' => 'طفل',
                'family' => 'أسرة',
                default => $row['kafel_type']
            };
        }

        // Format kafel_frequency
        $kafelFrequency = 'ليس كفالة يتيم';
        if ($row['kafel_frequency']) {
            $kafelFrequency = match ($row['kafel_frequency']) {
                'one-time' => 'لمرة واحدة',
                'monthly' => 'شهري',
                default => $row['kafel_frequency']
            };
        }

        // Format date
        $dateTime = new DateTime($row['donation_date']);
        $formattedDate = $dateTime->format('Y-m-d H:i:s');

        // Prepare row data
        $rowData = [
            $row['id'],
            $row['project_title'],
            $row['name'],
            $row['country'],
            $row['whatsapp'],
            $row['email'],
            $row['payment_method'],
            $row['amount'],
            $row['currency'],
            $row['number_shares'],
            $row['purpose'],
            $kafelType,
            $kafelFrequency,
            $formattedDate
        ];

        // Write row data
        if ($format === 'csv') {
            // Write row to CSV
            fputcsv($output, $rowData);
        } else {
            // Write row to HTML table
            echo '<tr>';
            foreach ($rowData as $cell) {
                echo '<td>' . htmlspecialchars($cell) . '</td>';
            }
            echo '</tr>';
        }
    }
}

if ($format === 'csv') {
    // Close the output stream for CSV
    fclose($output);
} else {
    // Close HTML table and document
    echo '</table>';
    echo '</body>';
    echo '</html>';
}
exit;
