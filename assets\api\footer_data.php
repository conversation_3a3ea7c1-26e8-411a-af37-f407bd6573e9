<?php
// الاتصال بقاعدة البيانات
require($_SERVER['DOCUMENT_ROOT'] . '/config/conn.php');

// جلب البيانات من جدول settings
$query = "SELECT * FROM settings LIMIT 1";
$result = mysqli_query($conn, $query);

if ($result && mysqli_num_rows($result) > 0) {
    $settings = mysqli_fetch_assoc($result);
    $whatsapp = $settings['whatsapp'];
    $email = $settings['email'];
    $facebook_url = $settings['facebook_url'];
    $twitter_url = $settings['twitter_url'];
    $telegram_url = $settings['telegram_url'];
    $instagram_url = $settings['instagram_url'];
    $tiktok_url = $settings['tiktok_url'];
    $youtube_url = $settings['youtube_url'];
    $linkedin_url = $settings['linkedin_url'];

} else {
    // تعريف قيم فارغة في حال لم يتم العثور على البيانات
    $whatsapp = '';
    $email = '';
    $facebook_url = '';
    $twitter_url = '';
    $telegram_url = '';
    $instagram_url = '';
    $tiktok_url = '';
    $youtube_url = '';
    $linkedin_url = '';
}

// جلب أحدث الأخبار
$projectsQuery = "SELECT title, image_path FROM projects ORDER BY created_at DESC LIMIT 3";
$projectsResult = mysqli_query($conn, $projectsQuery);
$projects = [];
if ($projectsResult) {
    while ($project = mysqli_fetch_assoc($projectsResult)) {
        $projects[] = $project;
    }
}

// جلب المقالات من المدونة
$articlesQuery = "SELECT id, title, image FROM articles ORDER BY created_at DESC LIMIT 3";
$articlesResult = mysqli_query($conn, $articlesQuery);
$articles = [];
if ($articlesResult) {
    while ($article = mysqli_fetch_assoc($articlesResult)) {
        $articles[] = $article;
    }
}

mysqli_close($conn);

// إرجاع البيانات بتنسيق JSON
echo json_encode([
    'settings' => [
        'whatsapp' => $whatsapp,
        'email' => $email,
        'facebook_url' => $facebook_url,
        'twitter_url' => $twitter_url,
        'telegram_url' => $telegram_url,
        'instagram_url' => $instagram_url,
        'tiktok_url' => $tiktok_url,
        'youtube_url' => $youtube_url,
        'linkedin_url' => $linkedin_url,

    ],
    'projects' => $projects,
    'articles' => $articles,
]);
