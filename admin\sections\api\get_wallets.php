<?php
include '../../../config/conn.php'; // تأكد من تضمين ملف الاتصال بقاعدة البيانات

$query = "SELECT * FROM wallet_addresses WHERE status = 'active' ORDER BY sort_order ASC";
$result = mysqli_query($conn, $query);

$wallets = [];
while ($row = mysqli_fetch_assoc($result)) {
    // تحويل العنوان من JSON إلى مصفوفة
    $addresses = json_decode($row['address'], true); // تحويل JSON إلى مصفوفة
    $row['address'] = implode(", ", $addresses); // دمج العناوين في سلسلة نصية مفصولة بفواصل

    // التأكد من وجود العمود 'image' في السطر الخاص بهذه الوسيلة
    if (isset($row['method_image'])) {
        $row['image'] = '../../../assets/img/wallets/' . $row['method_image']; // التأكد من وجود الصورة في المسار المناسب
    } else {
        $row['image'] = ''; // إذا لم توجد صورة، ترك الحقل فارغاً
    }

    $wallets[] = $row;
}

header('Content-Type: application/json'); // إضافة هذه السطر لتحديد نوع المحتوى
echo json_encode($wallets);
?>
