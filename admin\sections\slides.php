<?php
require '../config/conn.php';

// تأكد من تضمين مكتبة SweetAlert2 إذا لم تكن مضمنة بالفعل في الصفحة الرئيسية
if (!isset($sweetalert_included)) {
    echo '<script src="/assets/sweetalert/<EMAIL>"></script>';
    $sweetalert_included = true;
}

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['submit'])) {
    $title = $_POST['title'];
    $desc = $_POST['desc'];
    $image = $_FILES['image']['name'];
    $imageExtension = pathinfo($image, PATHINFO_EXTENSION);

    $allowedExtensions = ['png', 'jpg', 'jpeg'];
    if (in_array(strtolower($imageExtension), $allowedExtensions)) {
        $randomImageName = uniqid() . '.' . $imageExtension;
        $target = "../assets/img/slider_img/" . basename($randomImageName);

        if (move_uploaded_file($_FILES['image']['tmp_name'], $target)) {
            $query = "INSERT INTO slides (title, description, image_url, link_url) VALUES (?, ?, ?, ?)";
            $stmt = $conn->prepare($query);
            $stmt->bind_param('ssss', $title, $desc, $randomImageName, $_POST['link_url']);

            if ($stmt->execute()) {
                // تخزين معلمة page الحالية
                $currentPage = isset($_GET['page']) ? $_GET['page'] : '';

                // إعادة توجيه المستخدم إلى نفس الصفحة مع الحفاظ على معلمة page وإضافة معلمة scroll
                $redirectUrl = $_SERVER['PHP_SELF'] . '?page=' . $currentPage . '&scroll=existing-slides';

                echo '<script>
                        window.onload = function(e) {
                            Swal.fire({
                                icon: "success",
                                title: "تم بنجاح",
                                text: "تمت إضافة الشريحة بنجاح",
                                confirmButtonText: "حسناً"
                            }).then(() => {
                                window.location.href = "' . $redirectUrl . '";
                            });
                        };
                      </script>';
            } else {
                echo '<script>
                        window.onload = function(e) {
                            Swal.fire({
                                icon: "error",
                                title: "خطأ",
                                text: "خطأ في إضافة الشريحة: ' . $stmt->error . '",
                                confirmButtonText: "حسناً"
                            });
                        };
                      </script>';
            }
        } else {
            echo '<script>
                    window.onload = function(e) {
                        Swal.fire({
                            icon: "error",
                            title: "خطأ",
                            text: "فشل في تحميل الصورة.",
                            confirmButtonText: "حسناً"
                        });
                    };
                  </script>';
        }
    } else {
        echo '<script>
                window.onload = function(e) {
                    Swal.fire({
                        icon: "error",
                        title: "صيغة غير مدعومة",
                        text: "امتداد الصورة غير مدعوم. يرجى تحميل صورة بصيغة PNG أو JPG أو JPEG.",
                        confirmButtonText: "حسناً"
                    });
                };
              </script>';
    }
}


// جلب الشرائح من قاعدة البيانات
$query = "SELECT * FROM slides ORDER BY id DESC";
$result = $conn->query($query);
?>

<div class="p-6 bg-white rounded-lg shadow-md">
    <h2 class="text-2xl mb-4 text-center">إضافة شريحة جديدة</h2>

    <form action="" method="POST" enctype="multipart/form-data" class="space-y-6" id="add-slide-form">
        <div>
            <label for="title" class="block text-gray-700 mb-2 font-medium text-right">عنوان الشريحة</label>
            <input type="text" name="title" id="title" required class="border border-gray-300 p-3 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-blue-500 text-right" />
        </div>

        <div>
            <label for="desc" class="block text-gray-700 mb-2 font-medium text-right">وصف الشريحة</label>
            <textarea name="desc" id="desc" required class="border border-gray-300 p-3 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-blue-500 text-right"></textarea>
        </div>

        <div class="rtl">
            <label for="image" class="block text-gray-700 mb-2 font-medium text-right">صورة الشريحة</label>
            <input type="file" name="image" id="image" required class="file-input file-input-bordered border border-gray-300 p-3 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-blue-500 text-right" />
            <span class="text-xs text-gray-500">يفضل صورة بأبعاد 720×1280 بكسل</span>
        </div>

        <div>
            <label for="link_url" class="block text-gray-700 mb-2 font-medium text-right">رابط موضوع (اختياري)</label>
            <input type="url" name="link_url" id="link_url" class="border border-gray-300 p-3 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-blue-500 text-right" />
        </div>

        <button type="submit" name="submit" class="btn btn-primary w-full p-3 rounded-lg hover:bg-blue-600 transition">إضافة شريحة</button>
    </form>


    <h2 class="text-2xl mt-8 mb-4 text-right" id="existing-slides">الشرائح الموجودة</h2>
    
    <div class="space-y-4">
        
        <?php if ($result->num_rows === 0): ?>
           <div class="text-center text-gray-600 bg-yellow-100 border border-yellow-300 p-4 rounded-lg">
              لا توجد شرائح مضافة حالياً. يمكنك البدء بإضافة شريحة جديدة من النموذج أعلاه.
           </div>
            <?php else: ?>
    
            <?php while ($slide = $result->fetch_assoc()): ?>
            <div class="bg-gray-100 p-4 rounded-lg flex items-center justify-between flex-row-reverse shadow-sm">
                <div class="flex items-center flex-row-reverse">
                    <img src="../assets/img/slider_img/<?php echo $slide['image_url']; ?>" alt="<?php echo $slide['title']; ?>" class="w-32 h-32 object-cover rounded-lg mr-4" />
                    <div class="text-right mr-10">
                        <h3 class="text-lg mb-5"><?php echo $slide['title']; ?></h3>
                        <p class="text-gray-600 max-w-3xl"><?php echo $slide['description']; ?></p>
                    </div>
                </div>
                <div>
                    <div class="inline flex gap-2">
                        <input type="hidden" name="slide_id" value="<?php echo $slide['id']; ?>" />
                        <button type="button" onclick="edit_slide(<?php echo htmlspecialchars(json_encode($slide)); ?>)" class="bg-blue-500 text-white p-2 rounded-lg hover:bg-blue-600 transition">تعديل</button>
                        <button type="button" onclick="delete_slide('<?php echo $slide['id']; ?>')" class="bg-red-500 text-white p-2 rounded-lg hover:bg-red-600 transition">حذف</button>
                    </div>
                </div>
            </div>
            <?php endwhile; ?>
        <?php endif; ?>
    
    </div>
    
</div>

<script>
    // التمرير إلى قسم الشرائح الموجودة بعد إعادة تحميل الصفحة إذا كان هناك معلمة في الرابط
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('scroll') === 'existing-slides') {
            setTimeout(() => {
                document.getElementById('existing-slides').scrollIntoView({ behavior: 'smooth' });
            }, 500); // تأخير قصير للتأكد من تحميل الصفحة بالكامل
        }
    });

    // منع تداخل النماذج عند الضغط على أزرار الحذف والتعديل
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة مستمع للأحداث على أزرار الحذف والتعديل
        document.querySelectorAll('.bg-red-500, .bg-blue-500').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault(); // منع السلوك الافتراضي
                e.stopPropagation(); // منع انتشار الحدث
            });
        });
    });

    function delete_slide(id) {
        // منع السلوك الافتراضي للزر
        event.preventDefault();

        // استخدام SweetAlert بدلاً من confirm
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "هل أنت متأكد من أنك تريد حذف هذه الشريحة؟",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'نعم، قم بالحذف',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                fetch('/admin/sections/api/delete_slide.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'slide_id=' + id
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            Swal.fire({
                                icon: 'success',
                                title: 'تم الحذف',
                                text: data.message,
                                confirmButtonText: 'حسناً'
                            }).then(() => {
                                // الحصول على معلمة page الحالية
                                const urlParams = new URLSearchParams(window.location.search);
                                const pageParam = urlParams.get('page');

                                // إعادة تحميل الصفحة مع الحفاظ على معلمة page
                                window.location.href = window.location.pathname + "?page=" + pageParam;
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'خطأ',
                                text: data.message || 'حدث خطأ أثناء حذف الشريحة',
                                confirmButtonText: 'حسناً'
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ',
                            text: 'حدث خطأ في الاتصال بالخادم',
                            confirmButtonText: 'حسناً'
                        });
                    });
            }
        });

        return false;
    }

    function edit_slide(slide) {
        Swal.fire({
            title: 'تعديل الشريحة',
            width: '80%',
            html: `
                <form id="edit-slide-form" class="space-y-4 rtl text-right" enctype="multipart/form-data">
                    <input type="hidden" id="slide_id" name="slide_id" value="${slide.id}">

                    <div class="mb-4">
                        <label for="title" class="block text-gray-700 mb-2 font-medium">عنوان الشريحة</label>
                        <input type="text" id="title" name="title" value="${slide.title}" required class="border border-gray-300 p-3 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-blue-500 text-right">
                    </div>

                    <div class="mb-4">
                        <label for="desc" class="block text-gray-700 mb-2 font-medium">وصف الشريحة</label>
                        <textarea id="desc" name="desc" required class="border border-gray-300 p-3 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-blue-500 text-right">${slide.description}</textarea>
                    </div>

                    <div class="mb-4">
                        <label for="link_url" class="block text-gray-700 mb-2 font-medium">رابط موضوع (اختياري)</label>
                        <input type="url" id="link_url" name="link_url" value="${slide.link_url || ''}" class="border border-gray-300 p-3 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-blue-500 text-right">
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2 font-medium">الصورة الحالية</label>
                        <img src="../assets/img/slider_img/${slide.image_url}" alt="${slide.title}" class="w-full h-40 object-cover rounded-lg mb-2">
                        <label for="image" class="block text-gray-700 mb-2 font-medium">تغيير الصورة (اختياري)</label>
                        <input type="file" id="image" name="image" class="file-input file-input-bordered border border-gray-300 p-3 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-blue-500 text-right">
                    </div>
                </form>
            `,
            showCancelButton: true,
            confirmButtonText: 'حفظ التغييرات',
            cancelButtonText: 'إلغاء',
            customClass: {
                popup: 'rtl',
                confirmButton: 'bg-blue-500 text-white p-2 rounded-lg hover:bg-blue-600 transition',
                cancelButton: 'bg-gray-500 text-white p-2 rounded-lg hover:bg-gray-600 transition'
            },
            preConfirm: () => {
                const form = document.getElementById('edit-slide-form');
                const formData = new FormData(form);

                return fetch('/admin/sections/api/update_slide.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            Swal.fire({
                                icon: 'success',
                                title: 'تم التحديث',
                                text: data.message,
                                confirmButtonText: 'حسناً'
                            }).then(() => {
                                // الحصول على معلمة page الحالية
                                const urlParams = new URLSearchParams(window.location.search);
                                const pageParam = urlParams.get('page');

                                // إعادة تحميل الصفحة مع الحفاظ على معلمة page
                                window.location.href = window.location.pathname + "?page=" + pageParam;
                            });
                        } else {
                            Swal.showValidationMessage(`خطأ: ${data.message}`);
                        }
                    })
                    .catch(error => {
                        Swal.showValidationMessage(`خطأ في الاتصال: ${error.message}`);
                    });
            }
        });
    }
</script>