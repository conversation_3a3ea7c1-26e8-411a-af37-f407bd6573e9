<?php
// session_start();
$language = $_SESSION['lang'] ?? 'ar';

$todayGregorian = new DateTime('now', new DateTimeZone('Asia/Riyadh'));

// تنسيق التاريخ الميلادي باللغة العربية
$fmt = new IntlDateFormatter(
    'ar_EG', 
    IntlDateFormatter::NONE,  
    IntlDateFormatter::NONE,
    'Asia/Riyadh', 
    IntlDateFormatter::GREGORIAN,
    'EEEE الموافق d MMMM y '
);
$gregorianDate = $fmt->format($todayGregorian);

// الاتصال بقاعدة البيانات
require($_SERVER['DOCUMENT_ROOT'] . '/config/conn.php');

$query = "SELECT whatsapp, email FROM settings LIMIT 1";
$result = mysqli_query($conn, $query);

// التحقق من نجاح عملية جلب البيانات
if ($result && mysqli_num_rows($result) > 0) {
    $settings = mysqli_fetch_assoc($result);
    $whatsapp = $settings['whatsapp'];
    $email = $settings['email'];
} else {
    $whatsapp = '';
    $email = '';
}

mysqli_close($conn);
?>

<link rel="stylesheet" href="/assets/tailwind/tailwind.min.css">
<link rel="stylesheet" href="/assets/daisy/full.min.css">
<link rel="stylesheet" href="/assets/css/all.min.css">
<link rel="stylesheet" href="/assets/animate/animate.min.css">
<script src="/assets/sweetalert/<EMAIL>"></script>

<style>
    .navbar {
        min-height: unset;
    }

    .btn-menu {
        display: none;
    }

    @media screen and (max-width: 1020px) {

        .mini-nav,
        .right {
            flex-direction: column;
            align-items: flex-end;
            gap: 10px;
        }

        .btn-menu {
            display: flex;
        }
    }
</style>

<header class="relative w-full z-50 shadow-md">
    <div class="header">
        <div class="nav bg-cover flex h-60 bg-center" style="background-image: url('/assets/img/nav.png');padding: 10px 20px;height:100px">
            <div class="navbar flex justify-between items-center px-4 py-4">
                <div class="navbar-start">
                    <div class="dropdown">
                        <div tabindex="0" role="button" class="btn btn-menu btn-ghost">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h8m-8 6h16" />
                            </svg>
                        </div>
                        <ul tabindex="0" class="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow-lg transition-transform duration-200">
                            <?php
                            $menuItems = [
                                ['name' => 'الرئيسية', 'icon' => 'fas fa-home', 'url' => '/'],
                                ['name' => 'مشاريعنا', 'icon' => 'fas fa-building', 'url' => '/projects'],
                                ['name' => 'مقالاتنا', 'icon' => 'fas fa-file-alt', 'url' => '/blog'],
                                ['name' => 'نبذة عنا', 'icon' => 'fas fa-info-circle', 'url' => '/about'],
                                ['name' => 'تواصل معنا', 'icon' => 'fab fa-whatsapp', 'url' => 'https://wa.me/' . $whatsapp]
                            ];

                            foreach ($menuItems as $item) {
                                echo "<li class='transition-colors duration-200 rounded hover:bg-gray-100'>
                                        <a href='{$item['url']}' class='flex items-center px-3 py-2 text-gray-700 text-lg'>
                                            <i class='{$item['icon']} mr-2' style='color: #c3875d;'></i>
                                            {$item['name']}
                                        </a>
                                    </li>";
                            }
                            ?>
                        </ul>
                    </div>
                    <a href="/index#projects-section" class="btn text-base-100 ml-4 animate__animated animate__pulse animate__infinite" style="background-color: #c3875d; border-radius: 1.5rem; animation-duration: 1s;">تبرع لغزة الآن <i class="fas fa-donate"></i></a>
                </div>

                <div class="navbar-center hidden lg:flex">
                    <ul class="menu menu-horizontal flex-row-reverse px-1" style="flex-direction: row-reverse;">
                        <?php foreach ($menuItems as $index => $item) : ?>
                            <li class="transition-colors duration-200 rounded animate__animated animate__fadeInRight" style="animation-delay: <?php echo $index * 0.2; ?>s;">
                                <a href="<?php echo $item['url']; ?>" class="block px-4 py-2 text-gray-700 text-xl">
                                    <?php echo $item['name']; ?>
                                    <i class="<?php echo $item['icon']; ?> mr-2"></i>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>

                <div class="navbar-end flex items-center">
                    <a href="/"><img src="/assets/img/logo.png" alt="شعار الموقع" class="w-40 h-40 object-contain rounded-lg" /></a>
                </div>
            </div>
        </div>
        <div class="mini-nav navbar flex justify-between flex-row-reverse items-center" style="background-color: #c3875d;padding: 10px 30px">
            <div class="right flex gap-10 flex-row-reverse items-baseline">
                <div class="text-base-100 flex text-right items-baseline gap-2">
                    <span class="text-sm"><?php echo $gregorianDate; ?></span>
                    <i class="fa fa-calendar" aria-hidden="true"></i>
                </div>
            </div>
        </div>
    </div>
</header>