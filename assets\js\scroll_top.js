        const backToTopButton = document.getElementById('back-to-top');
        const whatsappBtn = document.getElementById('whatsapp-btn');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.remove('opacity-0', 'invisible');
                backToTopButton.classList.add('opacity-100', 'visible');
                whatsappBtn.classList.add('bottom-24'); // يتحرك الواتساب لأعلى
            } else {
                backToTopButton.classList.remove('opacity-100', 'visible');
                backToTopButton.classList.add('opacity-0', 'invisible');
                whatsappBtn.classList.remove('bottom-24');
            }
        });

        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });