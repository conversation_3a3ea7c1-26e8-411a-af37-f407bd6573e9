<?php
function createSlug($string)
{
    return urlencode(preg_replace('/\s+/u', '-', trim($string)));
}
?>
<style>
    .rtl {
        direction: rtl;
        text-align: right;
    }

</style>

<script src="https://cdn.tiny.cloud/1/ojfbr625zqz9xaagaewpfqj6assxoypa1fmyv9dxlfekxtpo/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>

<div class="rtl max-w-6xl mx-auto bg-white shadow-md rounded-lg p-8 animate__animated animate__fadeIn">
    <h1 class="text-3xl font-bold mb-6 text-center text-gray-800" id="form-title">إنشاء مقال جديد</h1>

    <form id="create-article-form" enctype="multipart/form-data" class="space-y-6">
        <div class="form-control">
            <label for="title" class="label font-semibold">عنوان المقال:</label>
            <input type="text" id="title" name="title" class="input input-bordered w-full" required>
        </div>

        <div class="form-control">
            <label for="views" class="label font-semibold">عدد المشاهدات:</label>
            <input type="number" id="views" name="views" class="input input-bordered w-full" min="0" required>
        </div>

        <div class="form-control">
            <label for="image" class="label font-semibold">صورة المقال:</label>
            <input type="file" id="image" name="image" class="file-input input-bordered w-full" accept="image/*">
        </div>

        <div class="form-control">
            <label for="content" class="label font-semibold">محتوى المقال:</label>
            <textarea id="content" name="content" class="textarea textarea-bordered h-64"></textarea>
        </div>

        <div id="btn">
            <button id="save_article" type="submit" class="btn btn-primary w-full animate__animated animate__pulse">إنشاء المقال</button>
        </div>
    </form>

    <div id="all_article" class="all_article mt-8">
        <h2 class="text-2xl font-semibold mb-4">جميع المقالات</h2>
        <div id="articles-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- المقالات ستُضاف هنا عبر AJAX -->
        </div>
    </div>

</div>

<script defer>
    document.addEventListener("DOMContentLoaded", function() {

        tinymce.init({
            selector: 'textarea',
            plugins: [
                'anchor', 'autolink', 'charmap', 'codesample', 'emoticons', 'image', 'link', 'lists', 'media', 'searchreplace', 'table', 'visualblocks', 'wordcount',
                'checklist', 'mediaembed', 'casechange', 'export', 'formatpainter', 'pageembed', 'a11ychecker', 'tinymcespellchecker', 'permanentpen', 'powerpaste',
                'advtable', 'advcode', 'editimage', 'advtemplate', 'mentions', 'tinycomments', 'tableofcontents', 'footnotes', 'mergetags', 'autocorrect',
                'typography', 'inlinecss', 'markdown', 'importword', 'exportword', 'exportpdf'
            ],
            toolbar: 'undo redo | bold italic underline | alignleft aligncenter alignright | fontselect fontsizeselect | forecolor backcolor | emoticons',
            tinycomments_mode: 'embedded',
            tinycomments_author: 'Author name',
            mergetags_list: [{
                    value: 'First.Name',
                    title: 'First Name'
                },
                {
                    value: 'Email',
                    title: 'Email'
                }
            ],
            directionality: 'rtl'
        });


        // دالة لتحميل المقالات باستخدام AJAX
        function loadArticles() {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', '/admin/sections/api/get_articles.php', true);
            xhr.onload = function() {
                if (xhr.status === 200) {

                    if (!xhr.responseText.trim()) {
                        alert('استجابة فارغة من الخادم');
                        return;
                    }

                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success && Array.isArray(response.articles)) {
                            displayArticles(response.articles);
                        } else {
                            alert(response.message || 'بيانات المقالات غير صحيحة');
                        }
                    } catch (e) {
                        alert('خطأ في تحليل JSON: ' + e.message);
                        console.error('Error parsing JSON:', e);
                    }
                } else {
                    alert('فشل الاتصال بالخادم');
                }
            };

            xhr.send();
        }
        loadArticles();

        // دالة لعرض المقالات
        function displayArticles(articles) {
            const container = document.getElementById('articles-container');
            container.innerHTML = ''; 

            articles.forEach(article => {
                const articleElement = document.createElement('div');
                articleElement.classList.add('bg-white', 'shadow-lg', 'rounded-lg', 'overflow-hidden', 'animate__animated', 'animate__fadeIn');

                // تنسيق التاريخ هنا
                const dateObj = new Date(article.date); 
                const formattedDate = dateObj.toLocaleDateString("ar-EG", {
                    day: "2-digit",
                    month: "2-digit",
                    year: "numeric"
                });

                // استبدال المسافات بشرطات في العنوان
                const formattedTitle = article.title.replace(/\s+/g, '-'); 

                articleElement.innerHTML = `
            <img src="../assets/img/blog/${article.image}" alt="${article.title}" class="w-full h-48 object-cover">
            <div class="p-4">
                <div>
                    <div class="data_article">
                        <h3 class="text-lg font-bold text-gray-800">${article.title}</h3>
                    </div>
                    <div class="flex justify-between items-center mt-4">
                        <span class="text-sm text-gray-500">عدد المشاهدات: ${article.views}</span>
                    </div>
                    <div class="flex justify-between items-center mt-4">
                        <span class="text-sm text-gray-500"> التاريخ: ${formattedDate}</span>
                    </div>
                </div>
                <div class="flex items-center justify-center gap-3 w-full mt-4">
                    <a href="../blog/article/${article.id}/${formattedTitle}" class="btn btn-primary text-center">
                        مشاهدة
                    </a>
                    <a href="javascript:void(0);" class="btn btn-error  text-center" id="delete-article-${article.id}">
                        حذف
                    </a>
                </div>
            </div>
        `;
                container.appendChild(articleElement);

                document.getElementById(`delete-article-${article.id}`).addEventListener('click', function() {
                    delete_article(article.id);
                });
            });
        }


        // دالة لإنشاء المقال
        function create_article(event) {
            event.preventDefault(); 

            const title = document.getElementById('title').value;
            const views = document.getElementById('views').value;
            const content = tinymce.get('content').getContent();
            const image = document.getElementById('image').files[0];

            if (!title || !views || !content) {
                alert('يرجى ملء جميع الحقول.');
                return;
            }

            const formData = new FormData();
            formData.append('title', title);
            formData.append('views', views);
            formData.append('content', content);
            formData.append('image', image);

            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/admin/sections/api/add_article.php', true);
            xhr.onload = function() {
                if (xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        alert('تم إنشاء المقال بنجاح!');
                        loadArticles();
                        resetForm();
                    } else {
                        alert(response.message || 'فشل إنشاء المقال');
                    }
                }
            };
            xhr.send(formData);
        }

        // دالة لحذف المقال
        function delete_article(id) {
            if (confirm('هل أنت متأكد من حذف المقال؟')) {
                const xhr = new XMLHttpRequest();
                xhr.open('POST', '/admin/sections/api/delete_article.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            loadArticles();
                            document.getElementById("all_article").scrollIntoView({
                                behavior: "smooth"
                            });
                        } else {
                            alert('حدث خطأ أثناء حذف المقال');
                        }
                    }
                };
                xhr.send('id=' + encodeURIComponent(id));
            }
        }

        document.getElementById('create-article-form').addEventListener('submit', create_article);

    });
</script>