<?php
// الاتصال بقاعدة البيانات
include '../../../config/conn.php';

// التحقق من أن الطلب هو POST وأن المحتوى هو JSON
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // قراءة البيانات المرسلة
    $json_data = file_get_contents('php://input');
    $data = json_decode($json_data, true);
    
    // التحقق من صحة البيانات
    if ($data && is_array($data)) {
        // بدء المعاملة
        mysqli_begin_transaction($conn);
        
        try {
            // تحديث ترتيب كل وسيلة دفع
            foreach ($data as $item) {
                $id = intval($item['id']);
                $order = intval($item['order']);
                
                // التحقق من صحة البيانات
                if ($id <= 0 || $order <= 0) {
                    throw new Exception("بيانات غير صالحة: المعرف أو الترتيب غير صحيح");
                }
                
                // تحديث الترتيب في قاعدة البيانات
                $query = "UPDATE wallet_addresses SET sort_order = ? WHERE id = ?";
                $stmt = mysqli_prepare($conn, $query);
                
                if (!$stmt) {
                    throw new Exception("خطأ في إعداد الاستعلام: " . mysqli_error($conn));
                }
                
                mysqli_stmt_bind_param($stmt, "ii", $order, $id);
                $result = mysqli_stmt_execute($stmt);
                
                if (!$result) {
                    throw new Exception("خطأ في تنفيذ الاستعلام: " . mysqli_stmt_error($stmt));
                }
                
                mysqli_stmt_close($stmt);
            }
            
            // تأكيد المعاملة
            mysqli_commit($conn);
            
            // إرسال استجابة نجاح
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'message' => 'تم تحديث ترتيب وسائل الدفع بنجاح'
            ]);
            
        } catch (Exception $e) {
            // التراجع عن المعاملة في حالة حدوث خطأ
            mysqli_rollback($conn);
            
            // إرسال استجابة خطأ
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    } else {
        // إرسال استجابة خطأ في حالة عدم صحة البيانات
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'بيانات غير صالحة'
        ]);
    }
} else {
    // إرسال استجابة خطأ في حالة استخدام طريقة غير مدعومة
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => 'طريقة الطلب غير مدعومة'
    ]);
}
?>
