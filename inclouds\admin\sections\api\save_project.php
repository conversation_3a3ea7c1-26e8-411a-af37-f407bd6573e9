<?php
// الاتصال بقاعدة البيانات
include '../../../config/conn.php';

// التأكد من أن الطلب تم إرساله باستخدام POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التأكد من وجود جميع البيانات المطلوبة
    $title = isset($_POST['title']) ? mysqli_real_escape_string($conn, $_POST['title']) : '';
    $description = isset($_POST['desc']) ? mysqli_real_escape_string($conn, $_POST['desc']) : '';
    $project_type = isset($_POST['project_type']) ? mysqli_real_escape_string($conn, $_POST['project_type']) : 'always';
    $project_kafel = isset($_POST['project_kafel']) ? mysqli_real_escape_string($conn, $_POST['project_kafel']) : 'no';
    $is_urgent = isset($_POST['is_urgent']) ? mysqli_real_escape_string($conn, $_POST['is_urgent']) : 'no';
    $amount = isset($_POST['amount']) ? (float)$_POST['amount'] : 0.0;  // المبلغ المراد تجميعه

    // التعامل مع رفع الملف إذا تم رفع صورة
    $image_path = null;
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $imageTmpPath = $_FILES['image']['tmp_name'];
        $imageName = $_FILES['image']['name'];
        $imageExtension = strtolower(pathinfo($imageName, PATHINFO_EXTENSION));

        // التأكد من أن الامتداد مسموح به
        $allowedExtensions = ['jpg', 'jpeg', 'png'];
        if (!in_array($imageExtension, $allowedExtensions)) {
            echo json_encode(['success' => false, 'message' => 'امتداد الصورة غير مسموح به. يجب أن يكون JPG أو PNG أو JPEG.']);
            exit;
        }

        // توليد اسم عشوائي للصورة
        $newImageName = uniqid('project_', true) . '.' . $imageExtension;
        $uploadDir = '../../../assets/img/project/';
        $imagePath = $uploadDir . $newImageName;

        // نقل الصورة إلى مجلد الرفع
        if (move_uploaded_file($imageTmpPath, $imagePath)) {
            // تخزين اسم الصورة فقط في قاعدة البيانات
            $image_path = $newImageName;
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في رفع الصورة.']);
            exit;
        }
    }

    // إنشاء استعلام SQL لإدخال البيانات
    $query = "INSERT INTO projects (
                    title,
                    description,
                    image_path,
                    project_type,
                    project_kafel,
                    is_urgent,
                    remaining_amount
              ) VALUES (
                    '$title',
                    '$description',
                    '$image_path',
                    '$project_type',
                    '$project_kafel',
                    '$is_urgent',
                    $amount
              )";

    // تنفيذ الاستعلام والتحقق من نجاح الإدخال
    if (mysqli_query($conn, $query)) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء حفظ البيانات: ' . mysqli_error($conn)]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'طريقة الإرسال غير صالحة.']);
}
?>
