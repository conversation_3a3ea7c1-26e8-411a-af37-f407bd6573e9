<footer class="footer footer-center bg-gray-900 text-base-content p-8">
    <div class="container mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- Logo and Social Links -->
        <div class="text-center md:text-left" id="footer-social-links">
        </div>

        <!-- Latest News and Blog Sections -->
        <div class="md:col-span-2 space-y-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Latest News -->
                <div class="space-y-4 flex flex-col items-end" id="latest-news">
                </div>

                <!-- Blog Section -->
                <div class="space-y-4 flex flex-col items-end" id="blog-section">
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div id="contact-info">
        </div>
    </div>
    



</footer>

    <div>
    <a href="https://coder.un-tools.com" target="_blank" style="background-color: #c3875d;" class="flex justify-center items-center p-4 gap-2 text-base-100 font-medium transition">
        <i class="fas fa-external-link-alt text-xl"></i>
       Coder برمجه وتطوير 
    </a>
</div>
<script defer>
    window.onload = function() {
        var xhr = new XMLHttpRequest();
        var baseUrl = window.location.origin; 
        var apiUrl = baseUrl + '/assets/api/footer_data.php';

        xhr.open('GET', apiUrl, true);
        xhr.onreadystatechange = function() {
            if (xhr.readyState == 4 && xhr.status == 200) {
                var data = JSON.parse(xhr.responseText);

                // تحميل الروابط الاجتماعية
                var socialLinks = `
                <img src="/assets/img/logo.png" alt="Logo" class="mx-auto md:mx-0 mb-4 w-36">
                <p class="font-semibold text-lg mb-4 text-white">من الناس إلى الناس</p>
                <div class="flex flex-wrap justify-center md:justify-start gap-4">
                    <a href="${data.settings.instagram_url}" class="text-gray-400 hover:text-white text-xl"><i class="fab fa-instagram"></i></a>
                    <a href="${data.settings.facebook_url}" class="text-gray-400 hover:text-white text-xl"><i class="fab fa-facebook"></i></a>
                    <a href="${data.settings.twitter_url}" class="text-gray-400 hover:text-white text-xl"><i class="fab fa-twitter"></i></a>
                    <a href="${data.settings.telegram_url}" class="text-gray-400 hover:text-white text-xl"><i class="fab fa-telegram"></i></a>
                    <a href="${data.settings.tiktok_url}" class="text-gray-400 hover:text-white text-xl"><i class="fab fa-tiktok"></i></a>
                    <a href="${data.settings.youtube_url}" class="text-gray-400 hover:text-white text-xl"><i class="fab fa-youtube"></i></a>
                    <a href="${data.settings.linkedin_url}" class="text-gray-400 hover:text-white text-xl"><i class="fab fa-linkedin"></i></a>
                    <a href="https://wa.me/${data.settings.whatsapp}" class="text-gray-400 hover:text-white text-xl"><i class="fab fa-whatsapp"></i></a>
                </div>
            `;
                document.getElementById('footer-social-links').innerHTML = socialLinks;

                // تحميل الأخبار
                var latestNews = '<h3 class="text-lg font-semibold mb-4 border-b-2 border-orange-400 text-white pb-3" style="text-align: right;">أحدث المشاريع</h3>';
                data.projects.forEach(function(project) {
                    latestNews += `
                    <div class="flex items-center space-x-3">
                        <a href="/projects/index" class="flex flex-row-reverse gap-4 hover:opacity-80 transition-opacity">
                            <img src="/assets/img/project/${project.image_path}" alt="${project.title}" class="w-16 h-16 rounded-lg shadow-md">
                            <p class="text-sm text-white">${project.title}</p>
                        </a>
                    </div>
                `;
                });
                document.getElementById('latest-news').innerHTML = latestNews;

                // تحميل المقالات
                var blogSection = '<h3 class="text-lg mb-4 border-b-2 border-orange-400 text-white md:text-left pb-3" style="text-align: right;">من المدونة</h3>';
                data.articles.forEach(function(article) {
                    blogSection += `
                    <div class="flex items-center space-x-3">
                        <a href="/blog/article/${article.id}/${encodeURIComponent(article.title)}" class="flex flex-row-reverse gap-4 hover:opacity-80 transition-opacity">
                            <img src="/assets/img/blog/${article.image}" alt="${article.title}" class="w-16 h-16 rounded-lg shadow-md">
                            <p class="text-sm text-white">${article.title}</p>
                        </a>
                    </div>
                `;
                });
                document.getElementById('blog-section').innerHTML = blogSection;

                // تحميل معلومات الاتصال
                var contactInfo = `
                <h3 class="text-lg font-semibold mb-4 border-b-2 border-orange-400 inline-block text-white">اتصل بنا</h3>
                <p class="mb-2 text-white"><i class="fas fa-envelope"></i> <a href="mailto:${data.settings.email}" class="hover:underline">${data.settings.email}</a></p>
                <p class="mb-2 text-white"><i class="fab fa-whatsapp"></i> <a href="https://wa.me/${data.settings.whatsapp}" class="hover:underline">${data.settings.whatsapp}</a></p>
                <p class="text-white"><i class="fas fa-map-marker-alt"></i> خيمة في رفح - قطاع غزة - فلسطين</p>
                <div class="flex justify-center gap-4 mt-4">
                    <button class="text-white text-2xl" onclick="reloadPage('phone')">
                        <i class="fas fa-mobile-alt"></i>
                    </button>
                    <button class="text-white text-2xl" onclick="reloadPage('tablet')">
                        <i class="fas fa-tablet-alt"></i>
                    </button>
                    <button class="text-white text-2xl" onclick="reloadPage('desktop')">
                        <i class="fas fa-laptop"></i>
                    </button>
                </div>
            `;
                document.getElementById('contact-info').innerHTML = contactInfo;
            }
        };
        xhr.send();
    };

    function reloadPage(device) {
        location.reload();
    }
</script>