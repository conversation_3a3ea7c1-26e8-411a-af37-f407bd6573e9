<?php
require '../config/conn.php';

$id = intval($_GET['id'] ?? 0);

// تحديث عدد المشاهدات
$updateViewsSql = "UPDATE articles SET views = views + 1 WHERE id = ?";
$updateStmt = $conn->prepare($updateViewsSql);
$updateStmt->bind_param("i", $id);
$updateStmt->execute();
$updateStmt->close();

// جلب بيانات المقال
$sql = "SELECT * FROM articles WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $id);
$stmt->execute();
$article = $stmt->get_result()->fetch_assoc() ?: exit("المقالة غير موجودة.");

// استعلام لجلب 5 مقالات عشوائية
$randomArticlesSql = "SELECT * FROM articles ORDER BY RAND() LIMIT 5";
$randomArticlesResult = $conn->query($randomArticlesSql);

// إغلاق الاستعلام
$stmt->close();
?>


<!DOCTYPE html>
<html lang="ar" data-theme="light">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link rel="icon" type="image/png" href="/assets/img/favicon.png">

    <title><?= htmlspecialchars($article['title']) ?></title>

    <style>
        .rtl {
            direction: rtl;
        }
    </style>
</head>

<body class="bg-gray-50 font-sans text-gray-900 ">
    <div class="header">
        <?php require '../layout/header.php'; ?>
    </div>

    <div class="container mx-auto px-4 py-8 flex flex-col lg:flex-row gap-8 rtl">
        <!-- Main Content -->
        <main class="flex-1 bg-white rounded-lg shadow-lg p-6">
            <!-- Breadcrumbs -->
            <nav class="text-sm rtl mb-4">
                <ul class="flex gap-4">
                    <li><a href="/" class="text-blue-500 hover:text-blue-700">الرئيسية</a></li>
                    <li class="text-gray-500"> &raquo; </li>
                    <li><a href="/blog" class="text-blue-500 hover:text-blue-700">مقالات</a></li>
                    <li class="text-gray-500"> &raquo; </li>
                    <li class="text-gray-500">
                        <a href="/blog/article/<?= $article['id'] ?>/<?= str_replace(' ', '-', strtolower($article['title'])) ?>" class="text-blue-500 hover:text-blue-700">
                            <?= htmlspecialchars($article['title']) ?>
                        </a>
                    </li>
                </ul>
            </nav>

            <!-- Article Metadata -->
            <div class="text-sm text-gray-500 mb-6 rtl flex items-center gap-4">
                <div class="flex items-center gap-2">
                    <i class="fas fa-calendar-alt text-gray-400 mr-1" style="color:#c3875d;"></i>
                    <span><?= date('d-m-Y', strtotime($article['created_at'])) ?></span>
                </div>
                <div class="flex items-center gap-2">
                    <i class="fas fa-eye text-gray-400 mr-1" style="color:#c3875d;"></i>
                    <span><?= $article['views'] ?> المشاهدات</span>
                </div>
            </div>

            <h1 class="text-3xl font-bold text-gray-800 mb-6"><?= htmlspecialchars($article['title']) ?></h1>
            <img src="/assets/img/blog/<?= $article['image'] ?>" alt="<?= htmlspecialchars($article['title']) ?>" class="w-full h-80 object-cover rounded-lg mb-6">
            <div class="text-lg text-gray-700 leading-relaxed mb-6"><?= $article['content'] ?></div>

            <div class="flex gap-4 mt-8 justify-center">
                <a href="https://www.facebook.com/sharer/sharer.php?u=<?= urlencode('https://' . $_SERVER['HTTP_HOST'] . '/blog/article/' . $article['id']) ?>" target="_blank" class="btn flex items-center gap-2 px-4 py-2 rounded-lg shadow-md transition-colors duration-300" style="background-color: #1877F2; color: white;">
                    <i class="fab fa-facebook text-xl"></i>
                    <span class="hidden md:inline-block" style="font-family: cairo;">شارك على فيسبوك</span>
                </a>
                <a href="https://twitter.com/intent/tweet?url=<?= urlencode('https://' . $_SERVER['HTTP_HOST'] . '/blog/article/' . $article['id']) ?>&text=<?= urlencode($article['title']) ?>" target="_blank" class="btn flex items-center gap-2 px-4 py-2 rounded-lg shadow-md transition-colors duration-300" style="background-color: #1DA1F2; color: white;">
                    <i class="fab fa-twitter text-xl"></i>
                    <span class="hidden md:inline-block" style="font-family: cairo;">شارك على تويتر</span>
                </a>
                <a href="https://wa.me/?text=<?= urlencode('https://' . $_SERVER['HTTP_HOST'] . '/blog/article/' . $article['id']) ?>" target="_blank" class="btn flex items-center gap-2 px-4 py-2 rounded-lg shadow-md transition-colors duration-300" style="background-color: #25D366; color: white;">
                    <i class="fab fa-whatsapp text-xl"></i>
                    <span class="hidden md:inline-block" style="font-family: cairo;">شارك على واتساب</span>
                </a>
                <a href="https://t.me/share/url?url=<?= urlencode('https://' . $_SERVER['HTTP_HOST'] . '/blog/article/' . $article['id']) ?>&text=<?= urlencode($article['title']) ?>" target="_blank" class="btn flex items-center gap-2 px-4 py-2 rounded-lg shadow-md transition-colors duration-300" style="background-color: #0088cc; color: white;">
                    <i class="fab fa-telegram-plane text-xl"></i>
                    <span class="hidden md:inline-block" style="font-family: cairo;">شارك على تليجرام</span>
                </a>
            </div>

        </main>

        <!-- Sidebar -->
        <aside class="w-full lg:w-1/3 space-y-6">

            <!-- Beneficiaries Counter Box -->
            <div class="bg-white p-4 rounded-lg shadow-lg text-center">
                <h2 class="text-xl font-semibold mb-2">عدد الأسر المستفيدة حتى الآن</h2>
                <p class="text-3xl font-bold" style="color: #c3875d;">
                    <?php
                    require '../config/conn.php';
                    $settingsResult = $conn->query("SELECT beneficiary_family FROM settings LIMIT 1");
                    $settings = $settingsResult->fetch_assoc();
                    echo htmlspecialchars($settings['beneficiary_family']);
                    ?>
                </p>
                <p class="text-sm text-gray-500">شكرًا لدعمكم المستمر!</p>
            </div>

            <!-- Related Articles or Categories Section -->
            <div class="bg-white p-4 rounded-lg shadow-lg">
                <h2 class="text-xl font-semibold mb-4">اخترنا لك</h2>
                <ul class="space-y-4">
                    <?php while ($randomArticle = $randomArticlesResult->fetch_assoc()): ?>
                        <li>
                            <a class="flex items-center text-2xl gap-4" href="/blog/article/<?= $randomArticle['id'] ?>/<?= str_replace(' ', '-', strtolower($randomArticle['title'])) ?>" class="flex items-center space-x-4 text-blue-600 hover:underline">
                                <!-- Image Thumbnail -->
                                <img src="/assets/img/blog/<?= $randomArticle['image'] ?>" alt="<?= htmlspecialchars($randomArticle['title']) ?>" class="w-16 h-16 object-cover rounded-md">
                                <!-- Title -->
                                <span class="text-sm font-medium"><?= htmlspecialchars($randomArticle['title']) ?></span>
                            </a>
                        </li>
                    <?php endwhile; ?>
                </ul>
            </div>

        </aside>
    </div>

    <?php require '../layout/footer.php' ?>
</body>

</html>