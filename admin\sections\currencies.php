<?php
require '../config/conn.php';

// جلب جميع العملات من قاعدة البيانات
$query = "SELECT * FROM currencies ORDER BY id DESC";
$result = mysqli_query($conn, $query);

$currencies = [];
if ($result && mysqli_num_rows($result) > 0) {
    while ($row = mysqli_fetch_assoc($result)) {
        $currencies[] = $row;
    }
}
?>

<div class="container mx-auto p-6 rtl">
    <div class="bg-gradient-to-b from-white to-gray-50 rounded-2xl shadow-xl overflow-hidden">
        <!-- رأس الصفحة -->
        <div class="bg-gradient-to-r from-green-500 to-green-600 p-6 mb-8">
            <h2 class="text-3xl font-bold text-white flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                إدارة العملات
            </h2>
            <p class="text-green-100 mt-2">إضافة وتعديل وحذف العملات المستخدمة في النظام</p>
        </div>

        <div class="px-6 pb-8">
            <!-- نموذج إضافة عملة جديدة -->
            <div class="bg-white rounded-xl shadow-md p-6 mb-8 transform transition-all duration-300 hover:shadow-lg">
                <h3 class="text-xl font-semibold text-gray-800 mb-5 flex items-center border-b pb-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 ml-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    إضافة عملة جديدة
                </h3>
                <form id="add-currency-form" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium text-gray-700">اسم العملة بالعربية</span>
                        </label>
                        <input type="text" id="name_ar" name="name_ar" placeholder="أسم العملة بالعربي , بدون *ال*" class="input input-bordered w-full bg-gray-50 focus:bg-white transition-colors duration-200 focus:border-green-500 focus:ring-2 focus:ring-green-200" required>
                    </div>

                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium text-gray-700">رمز العملة</span>
                        </label>
                        <input type="text" id="symbol" name="symbol" class="input input-bordered w-full bg-gray-50 focus:bg-white transition-colors duration-200 focus:border-green-500 focus:ring-2 focus:ring-green-200" required maxlength="10" placeholder="مثال: USD, EUR, EGP">
                    </div>

                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium text-gray-700">قيمة السهم</span>
                        </label>
                        <input type="number" id="value" name="value" class="input input-bordered w-full bg-gray-50 focus:bg-white transition-colors duration-200 focus:border-green-500 focus:ring-2 focus:ring-green-200" required step="0.0001" min="0.0001" placeholder="قيمة السهم">
                    </div>

                    <div class="form-control md:col-span-3 mt-4 flex justify-center">
                        <button type="submit" id="add-currency-btn" class="btn btn-primary bg-green-500 hover:bg-green-600 text-white border-none px-8 py-3 rounded-full shadow-md hover:shadow-lg transform transition-all duration-300 hover:-translate-y-1">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            إضافة عملة
                        </button>
                    </div>
                </form>
            </div>

            <!-- جدول العملات -->
            <div class="bg-white rounded-xl shadow-md p-6 transform transition-all duration-300 hover:shadow-lg">
                <h3 class="text-xl font-semibold text-gray-800 mb-5 flex items-center border-b pb-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 ml-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                    </svg>
                    العملات المتوفرة
                </h3>
                <div id="currencies-table-container" class="overflow-x-auto rounded-lg">
                    <table class="table w-full">
                        <thead>
                            <tr class="bg-gradient-to-r from-green-500 to-green-600 text-white">
                                <th class="rounded-tr-lg">#</th>
                                <th>اسم العملة</th>
                                <th>رمز العملة</th>
                                <th>قيمة السهم</th>
                                <th class="rounded-tl-lg">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="currencies-table-body" class="divide-y divide-gray-100">
                            <?php if (empty($currencies)): ?>
                                <tr>
                                    <td colspan="5" class="text-center py-8 text-gray-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-3 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        لا توجد عملات مسجلة
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($currencies as $index => $currency): ?>
                                    <tr id="currency-row-<?php echo $currency['id']; ?>" class="hover:bg-gray-50 transition-colors duration-150">
                                        <td class="font-bold text-green-600"><?php echo $index + 1; ?></td>
                                        <td class="py-4"><?php echo htmlspecialchars($currency['name_ar']); ?></td>
                                        <td class="py-4 font-semibold"><?php echo htmlspecialchars($currency['symbol']); ?></td>
                                        <td class="py-4"><?php echo number_format($currency['value'], 2); ?></td>
                                        <td>
                                            <div class="flex gap-4 justify-end">
                                                <button onclick="editCurrency(<?php echo $currency['id']; ?>, '<?php echo htmlspecialchars($currency['name_ar']); ?>', '<?php echo htmlspecialchars($currency['symbol']); ?>', <?php echo $currency['value']; ?>)" class="btn btn-sm bg-blue-500 hover:bg-blue-600 text-white border-none rounded-lg shadow-sm hover:shadow-md transition-all duration-200">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                    </svg>
                                                </button>
                                                <button onclick="deleteCurrency(<?php echo $currency['id']; ?>)" class="btn btn-sm bg-red-500 hover:bg-red-600 text-white border-none rounded-lg shadow-sm hover:shadow-md transition-all duration-200">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                    </svg>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تعديل العملة -->
<div id="edit-currency-modal" class="modal">
    <div class="modal-box rtl bg-white rounded-2xl shadow-xl p-0 max-w-md mx-auto overflow-hidden">
        <!-- رأس النافذة -->
        <div class="bg-gradient-to-r from-green-500 to-green-600 p-4">
            <h3 class="font-bold text-xl text-white flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                تعديل العملة
            </h3>
        </div>

        <!-- محتوى النافذة -->
        <div class="p-6">
            <form id="edit-currency-form">
                <input type="hidden" id="edit_currency_id" name="id">

                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text font-medium text-gray-700">اسم العملة بالعربية</span>
                    </label>
                    <input type="text" id="edit_name_ar" name="name_ar" class="input input-bordered w-full bg-gray-50 focus:bg-white transition-colors duration-200 focus:border-green-500 focus:ring-2 focus:ring-green-200" required>
                </div>

                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text font-medium text-gray-700">رمز العملة</span>
                    </label>
                    <input type="text" id="edit_symbol" name="symbol" class="input input-bordered w-full bg-gray-50 focus:bg-white transition-colors duration-200 focus:border-green-500 focus:ring-2 focus:ring-green-200" required maxlength="10">
                </div>

                <div class="form-control mb-6">
                    <label class="label">
                        <span class="label-text font-medium text-gray-700">قيمة السهم</span>
                    </label>
                    <input type="number" id="edit_value" name="value" class="input input-bordered w-full bg-gray-50 focus:bg-white transition-colors duration-200 focus:border-green-500 focus:ring-2 focus:ring-green-200" required step="0.0001" min="0.0001">
                </div>

                <div class="flex justify-between gap-4">
                    <button type="button" onclick="closeEditModal()" class="btn btn-outline flex-1 border-gray-300 hover:bg-gray-100 hover:border-gray-300 text-gray-700 transition-all duration-200">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        إلغاء
                    </button>
                    <button type="submit" class="btn flex-1 bg-green-500 hover:bg-green-600 text-white border-none shadow-md hover:shadow-lg transition-all duration-200">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // دالة لتحديث جدول العملات
    function refreshCurrenciesTable() {
        // إظهار مؤشر التحميل في الجدول
        const tableBody = document.getElementById('currencies-table-body');
        tableBody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center py-8">
                    <div class="flex flex-col items-center justify-center">
                        <div class="loading loading-spinner loading-lg text-green-500 mb-3"></div>
                        <p class="text-gray-500">جاري تحميل البيانات...</p>
                    </div>
                </td>
            </tr>
        `;

        fetch('/admin/sections/api/get_currencies.php')
            .then(response => response.json())
            .then(data => {
                // تأخير بسيط لإظهار التأثير البصري للتحميل
                setTimeout(() => {
                    if (data.length === 0) {
                        tableBody.innerHTML = `
                            <tr>
                                <td colspan="5" class="text-center py-8 text-gray-500">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-3 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    لا توجد عملات مسجلة
                                </td>
                            </tr>
                        `;
                        return;
                    }

                    let tableContent = '';
                    data.forEach((currency, index) => {
                        tableContent += `
                            <tr id="currency-row-${currency.id}" class="hover:bg-gray-50 transition-colors duration-150 opacity-0 animate-fadeIn" style="animation-delay: ${index * 50}ms">
                                <td class="font-bold text-green-600">${index + 1}</td>
                                <td class="py-4">${currency.name_ar}</td>
                                <td class="py-4 font-semibold">${currency.symbol}</td>
                                <td class="py-4">${parseFloat(currency.value).toFixed(2)}</td>
                                <td>
                                    <div class="flex space-x-2 justify-end gap-4">
                                        <button onclick="editCurrency(${currency.id}, '${currency.name_ar}', '${currency.symbol}', ${currency.value})" class="btn btn-sm bg-blue-500 hover:bg-blue-600 text-white border-none rounded-lg shadow-sm hover:shadow-md transition-all duration-200">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                            </svg>
                                        </button>
                                        <button onclick="deleteCurrency(${currency.id})" class="btn btn-sm bg-red-500 hover:bg-red-600 text-white border-none rounded-lg shadow-sm hover:shadow-md transition-all duration-200">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `;
                    });

                    tableBody.innerHTML = tableContent;

                    // إضافة تأثير الظهور التدريجي للصفوف
                    document.querySelectorAll('#currencies-table-body tr').forEach(row => {
                        row.classList.add('animate-fadeIn');
                    });
                }, 300);
            })
            .catch(error => {
                console.error('Error fetching currencies:', error);
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center py-8 text-red-500">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-3 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                            حدث خطأ أثناء جلب بيانات العملات
                        </td>
                    </tr>
                `;

                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء جلب بيانات العملات',
                    confirmButtonColor: '#10b981'
                });
            });
    }

    // إضافة تأثير الظهور التدريجي للعناصر والإشعارات المخصصة
    document.head.insertAdjacentHTML('beforeend', `
        <style>
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }
            .animate-fadeIn {
                animation: fadeIn 0.5s ease forwards;
            }

            @keyframes scaleIn {
                from { opacity: 0; transform: scale(0.9); }
                to { opacity: 1; transform: scale(1); }
            }
            .animate-scaleIn {
                animation: scaleIn 0.3s ease forwards;
            }

            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
                20%, 40%, 60%, 80% { transform: translateX(5px); }
            }
            .animate-shake {
                animation: shake 0.5s ease;
            }

            .input-focus-effect:focus {
                box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
            }

            /* تنسيقات الإشعارات المخصصة */
            .custom-notification {
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%) translateY(-100px);
                background-color: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                padding: 16px;
                display: flex;
                align-items: center;
                min-width: 300px;
                max-width: 450px;
                z-index: 9999;
                opacity: 0;
                transition: transform 0.3s ease, opacity 0.3s ease;
                direction: rtl;
            }

            .custom-notification.show {
                transform: translateX(-50%) translateY(0);
                opacity: 1;
            }

            .success-notification {
                border-right: 4px solid #10b981;
            }

            .error-notification {
                border-right: 4px solid #ef4444;
            }

            .notification-icon {
                width: 24px;
                height: 24px;
                margin-left: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .success-notification .notification-icon {
                color: #10b981;
            }

            .error-notification .notification-icon {
                color: #ef4444;
            }

            .notification-content {
                flex: 1;
            }

            .notification-content h3 {
                margin: 0 0 4px 0;
                font-size: 16px;
                font-weight: 600;
                color: #1f2937;
            }

            .notification-content p {
                margin: 0;
                font-size: 14px;
                color: #6b7280;
            }

            .notification-close {
                background: none;
                border: none;
                color: #9ca3af;
                font-size: 18px;
                cursor: pointer;
                padding: 0;
                margin-right: 8px;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: background-color 0.2s ease;
            }

            .notification-close:hover {
                background-color: #f3f4f6;
                color: #4b5563;
            }
        </style>
    `);

    // إضافة عملة جديدة
    document.getElementById('add-currency-form').addEventListener('submit', function(e) {
        e.preventDefault();

        // التحقق من صحة المدخلات
        const nameAr = document.getElementById('name_ar').value.trim();
        const symbol = document.getElementById('symbol').value.trim();
        const value = parseFloat(document.getElementById('value').value);

        if (!nameAr || !symbol || isNaN(value) || value <= 0) {
            // تحريك النموذج للإشارة إلى وجود خطأ
            const form = document.getElementById('add-currency-form');
            form.classList.add('animate-shake');
            setTimeout(() => form.classList.remove('animate-shake'), 500);

            Swal.fire({
                icon: 'warning',
                title: 'تنبيه',
                text: 'يرجى التأكد من إدخال جميع البيانات بشكل صحيح',
                confirmButtonColor: '#10b981'
            });
            return;
        }

        const formData = new FormData();
        formData.append('name_ar', nameAr);
        formData.append('symbol', symbol);
        formData.append('value', value);

        // تعطيل زر الإضافة وإظهار مؤشر التحميل
        const addButton = document.getElementById('add-currency-btn');
        const originalButtonText = addButton.innerHTML;
        addButton.disabled = true;
        addButton.classList.add('opacity-70');
        addButton.innerHTML = '<span class="loading loading-spinner loading-sm ml-1"></span> جاري الإضافة...';

        fetch('/admin/sections/api/add_currency.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            // إعادة تفعيل زر الإضافة بعد تأخير بسيط
            setTimeout(() => {
                addButton.disabled = false;
                addButton.classList.remove('opacity-70');
                addButton.innerHTML = originalButtonText;

                if (data.success) {
                    // إعادة تعيين النموذج مع تأثير بصري
                    const inputs = document.querySelectorAll('#add-currency-form input');
                    inputs.forEach(input => {
                        input.classList.add('bg-green-50', 'border-green-200');
                        setTimeout(() => {
                            input.classList.remove('bg-green-50', 'border-green-200');
                            input.value = '';
                        }, 1000);
                    });

                    // تحديث جدول العملات
                    refreshCurrenciesTable();

                    // إنشاء عنصر إشعار مخصص
                    const notification = document.createElement('div');
                    notification.className = 'custom-notification success-notification';
                    notification.innerHTML = `
                        <div class="notification-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                        <div class="notification-content">
                            <h3>تم بنجاح</h3>
                            <p>تمت إضافة العملة بنجاح</p>
                        </div>
                        <button class="notification-close" onclick="this.parentElement.remove()">×</button>
                    `;

                    // إضافة الإشعار إلى الصفحة
                    document.body.appendChild(notification);

                    // إظهار الإشعار بتأثير بصري
                    setTimeout(() => {
                        notification.classList.add('show');
                    }, 10);

                    // إخفاء الإشعار بعد 3 ثوانٍ
                    setTimeout(() => {
                        notification.classList.remove('show');
                        setTimeout(() => {
                            notification.remove();
                        }, 300);
                    }, 3000);
                } else {
                    // عرض رسالة خطأ
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: data.message || 'حدث خطأ أثناء إضافة العملة',
                        confirmButtonText: 'حسناً',
                        confirmButtonColor: '#10b981',
                        customClass: {
                            popup: 'animate-scaleIn'
                        }
                    });
                }
            }, 600);
        })
        .catch(error => {
            console.error('Error adding currency:', error);

            // إعادة تفعيل زر الإضافة
            setTimeout(() => {
                addButton.disabled = false;
                addButton.classList.remove('opacity-70');
                addButton.innerHTML = originalButtonText;

                // عرض رسالة خطأ
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء الاتصال بالخادم',
                    confirmButtonText: 'حسناً',
                    confirmButtonColor: '#10b981',
                    customClass: {
                        popup: 'animate-scaleIn'
                    }
                });
            }, 600);
        });
    });

    // فتح نافذة تعديل العملة
    function editCurrency(id, name_ar, symbol, value) {
        // تحضير النافذة المنبثقة
        const modal = document.getElementById('edit-currency-modal');

        // تعيين قيم الحقول
        document.getElementById('edit_currency_id').value = id;
        document.getElementById('edit_name_ar').value = name_ar;
        document.getElementById('edit_symbol').value = symbol;
        document.getElementById('edit_value').value = value;

        // فتح النافذة المنبثقة مع تأثير بصري
        modal.classList.add('modal-open');

        // إضافة تأثير الظهور التدريجي للنافذة
        const modalBox = modal.querySelector('.modal-box');
        modalBox.style.opacity = '0';
        modalBox.style.transform = 'scale(0.9)';

        setTimeout(() => {
            modalBox.style.transition = 'all 0.3s ease';
            modalBox.style.opacity = '1';
            modalBox.style.transform = 'scale(1)';
        }, 10);

        // تركيز على الحقل الأول
        setTimeout(() => {
            document.getElementById('edit_name_ar').focus();
        }, 300);
    }

    // إغلاق نافذة تعديل العملة
    function closeEditModal() {
        const modal = document.getElementById('edit-currency-modal');
        const modalBox = modal.querySelector('.modal-box');

        // إضافة تأثير الاختفاء التدريجي
        modalBox.style.opacity = '0';
        modalBox.style.transform = 'scale(0.9)';

        setTimeout(() => {
            modal.classList.remove('modal-open');
            modalBox.style.transition = '';
        }, 200);
    }

    // تحديث بيانات العملة
    document.getElementById('edit-currency-form').addEventListener('submit', function(e) {
        e.preventDefault();

        // التحقق من صحة المدخلات
        const nameAr = document.getElementById('edit_name_ar').value.trim();
        const symbol = document.getElementById('edit_symbol').value.trim();
        const value = parseFloat(document.getElementById('edit_value').value);
        const id = document.getElementById('edit_currency_id').value;

        if (!nameAr || !symbol || isNaN(value) || value <= 0 || !id) {
            // تحريك النموذج للإشارة إلى وجود خطأ
            const form = document.getElementById('edit-currency-form');
            form.classList.add('animate-shake');
            setTimeout(() => form.classList.remove('animate-shake'), 500);

            Swal.fire({
                icon: 'warning',
                title: 'تنبيه',
                text: 'يرجى التأكد من إدخال جميع البيانات بشكل صحيح',
                confirmButtonColor: '#10b981'
            });
            return;
        }

        const formData = new FormData();
        formData.append('id', id);
        formData.append('name_ar', nameAr);
        formData.append('symbol', symbol);
        formData.append('value', value);

        // تعطيل أزرار النموذج وإظهار مؤشر التحميل
        const submitButton = document.querySelector('#edit-currency-form button[type="submit"]');
        const cancelButton = document.querySelector('#edit-currency-form button[type="button"]');
        const originalButtonText = submitButton.innerHTML;

        submitButton.disabled = true;
        cancelButton.disabled = true;
        submitButton.classList.add('opacity-70');
        submitButton.innerHTML = '<span class="loading loading-spinner loading-sm ml-1"></span> جاري الحفظ...';

        fetch('/admin/sections/api/update_currency.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            // إعادة تفعيل الأزرار بعد تأخير بسيط
            setTimeout(() => {
                submitButton.disabled = false;
                cancelButton.disabled = false;
                submitButton.classList.remove('opacity-70');
                submitButton.innerHTML = originalButtonText;

                if (data.success) {
                    // إغلاق النافذة المنبثقة
                    closeEditModal();

                    // تحديث جدول العملات
                    refreshCurrenciesTable();

                    // تمييز الصف المحدث في الجدول
                    setTimeout(() => {
                        const row = document.getElementById(`currency-row-${id}`);
                        if (row) {
                            row.classList.add('bg-green-50');
                            setTimeout(() => {
                                row.classList.remove('bg-green-50');
                            }, 2000);
                        }
                    }, 500);

                    // إنشاء عنصر إشعار مخصص
                    const notification = document.createElement('div');
                    notification.className = 'custom-notification success-notification';
                    notification.innerHTML = `
                        <div class="notification-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                        <div class="notification-content">
                            <h3>تم بنجاح</h3>
                            <p>تم تحديث بيانات العملة بنجاح</p>
                        </div>
                        <button class="notification-close" onclick="this.parentElement.remove()">×</button>
                    `;

                    // إضافة الإشعار إلى الصفحة
                    document.body.appendChild(notification);

                    // إظهار الإشعار بتأثير بصري
                    setTimeout(() => {
                        notification.classList.add('show');
                    }, 10);

                    // إخفاء الإشعار بعد 3 ثوانٍ
                    setTimeout(() => {
                        notification.classList.remove('show');
                        setTimeout(() => {
                            notification.remove();
                        }, 300);
                    }, 3000);
                } else {
                    // عرض رسالة خطأ
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: data.message || 'حدث خطأ أثناء تحديث بيانات العملة',
                        confirmButtonText: 'حسناً',
                        confirmButtonColor: '#10b981',
                        customClass: {
                            popup: 'animate-scaleIn'
                        }
                    });
                }
            }, 600);
        })
        .catch(error => {
            console.error('Error updating currency:', error);

            // إعادة تفعيل الأزرار
            setTimeout(() => {
                submitButton.disabled = false;
                cancelButton.disabled = false;
                submitButton.classList.remove('opacity-70');
                submitButton.innerHTML = originalButtonText;

                // عرض رسالة خطأ
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء الاتصال بالخادم',
                    confirmButtonText: 'حسناً',
                    confirmButtonColor: '#10b981',
                    customClass: {
                        popup: 'animate-scaleIn'
                    }
                });
            }, 600);
        });
    });

    // حذف عملة
    function deleteCurrency(id) {
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: 'سيتم حذف هذه العملة نهائياً',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'نعم، حذف',
            cancelButtonText: 'إلغاء',
            customClass: {
                popup: 'animate-scaleIn',
                confirmButton: 'btn btn-error',
                cancelButton: 'btn btn-ghost'
            },
            showClass: {
                popup: 'animate-scaleIn'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // تمييز الصف المراد حذفه
                const row = document.getElementById(`currency-row-${id}`);
                if (row) {
                    row.classList.add('bg-red-50');
                }

                const formData = new FormData();
                formData.append('id', id);

                fetch('/admin/sections/api/delete_currency.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    setTimeout(() => {
                        if (data.success) {
                            // حذف الصف من الجدول بتأثير بصري
                            if (row) {
                                row.style.transition = 'all 0.5s ease';
                                row.style.height = row.offsetHeight + 'px';

                                setTimeout(() => {
                                    row.style.height = '0';
                                    row.style.opacity = '0';
                                    row.style.padding = '0';
                                    row.style.margin = '0';
                                    row.style.overflow = 'hidden';

                                    setTimeout(() => {
                                        refreshCurrenciesTable();
                                    }, 300);
                                }, 100);
                            } else {
                                refreshCurrenciesTable();
                            }

                            // إنشاء عنصر إشعار مخصص
                            const notification = document.createElement('div');
                            notification.className = 'custom-notification success-notification';
                            notification.innerHTML = `
                                <div class="notification-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <div class="notification-content">
                                    <h3>تم الحذف</h3>
                                    <p>تم حذف العملة بنجاح</p>
                                </div>
                                <button class="notification-close" onclick="this.parentElement.remove()">×</button>
                            `;

                            // إضافة الإشعار إلى الصفحة
                            document.body.appendChild(notification);

                            // إظهار الإشعار بتأثير بصري
                            setTimeout(() => {
                                notification.classList.add('show');
                            }, 10);

                            // إخفاء الإشعار بعد 3 ثوانٍ
                            setTimeout(() => {
                                notification.classList.remove('show');
                                setTimeout(() => {
                                    notification.remove();
                                }, 300);
                            }, 3000);
                        } else {
                            // إزالة التمييز من الصف في حالة الفشل
                            if (row) {
                                row.classList.remove('bg-red-50');
                            }

                            // عرض رسالة خطأ
                            Swal.fire({
                                icon: 'error',
                                title: 'خطأ',
                                text: data.message || 'حدث خطأ أثناء حذف العملة',
                                confirmButtonText: 'حسناً',
                                confirmButtonColor: '#10b981',
                                customClass: {
                                    popup: 'animate-scaleIn'
                                }
                            });
                        }
                    }, 800);
                })
                .catch(error => {
                    console.error('Error deleting currency:', error);

                    // إزالة التمييز من الصف في حالة الفشل
                    if (row) {
                        row.classList.remove('bg-red-50');
                    }

                    // عرض رسالة خطأ
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء الاتصال بالخادم',
                        confirmButtonText: 'حسناً',
                        confirmButtonColor: '#10b981',
                        customClass: {
                            popup: 'animate-scaleIn'
                        }
                    });
                });
            }
        });
    }

    // تحميل بيانات العملات عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة تأثيرات الظهور التدريجي للعناصر الرئيسية
        const mainElements = document.querySelectorAll('.container > div > div');
        mainElements.forEach((element, index) => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            element.style.transition = `all 0.5s ease ${index * 0.2}s`;

            setTimeout(() => {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, 100);
        });

        // إضافة تأثيرات التفاعل للحقول
        const inputs = document.querySelectorAll('input');
        inputs.forEach(input => {
            input.classList.add('input-focus-effect');

            // إضافة تأثير عند التركيز
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('scale-105');
                this.parentElement.style.transition = 'transform 0.3s ease';
            });

            // إزالة التأثير عند فقدان التركيز
            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('scale-105');
            });
        });

        // إضافة تأثير النقر على الأزرار
        const buttons = document.querySelectorAll('button');
        buttons.forEach(button => {
            button.addEventListener('click', function(event) {
                // إضافة تأثير النقر فقط إذا لم يكن الزر معطلاً
                if (!this.disabled) {
                    const ripple = document.createElement('span');
                    ripple.classList.add('ripple');
                    this.appendChild(ripple);

                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);

                    ripple.style.width = ripple.style.height = `${size}px`;
                    ripple.style.left = `${event.clientX - rect.left - size / 2}px`;
                    ripple.style.top = `${event.clientY - rect.top - size / 2}px`;

                    ripple.classList.add('active');

                    setTimeout(() => {
                        ripple.remove();
                    }, 500);
                }
            });
        });

        // إضافة تأثير الظهور التدريجي لصفوف الجدول
        const tableRows = document.querySelectorAll('#currencies-table-body tr');
        tableRows.forEach((row, index) => {
            row.style.opacity = '0';
            row.style.transform = 'translateY(10px)';
            row.style.transition = `all 0.3s ease ${index * 0.05}s`;

            setTimeout(() => {
                row.style.opacity = '1';
                row.style.transform = 'translateY(0)';
            }, 300);
        });

        // إضافة تأثير الريبل للأزرار
        document.head.insertAdjacentHTML('beforeend', `
            <style>
                .ripple {
                    position: absolute;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple-animation 0.5s ease-out;
                    pointer-events: none;
                }

                @keyframes ripple-animation {
                    to {
                        transform: scale(2);
                        opacity: 0;
                    }
                }

                button {
                    position: relative;
                    overflow: hidden;
                }
            </style>
        `);
    });
</script>