<style>
    .wave {
        margin-top: -55px;
        width: 100%;
        overflow: hidden;
    }

    .waves-container,
    .waves {
        width: 100%;
        height: 200px;
        position: relative;
    }

    svg {
        width: 100%;
        height: 100%;
        display: block;
    }
</style>

<div class="wave">
    <div class="waves-container">
        <div class="waves bg-gray-900">
            <svg viewBox="0 0 1920 200" preserveAspectRatio="none" fill="none" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" stop-color="#1f2937"></stop>
                        <stop offset="50%" stop-color="#1f2937"></stop>
                        <stop offset="100%" stop-color="#1f2937"></stop>
                    </linearGradient>
                </defs>
                <path fill="url(#grad1)" d="M0 133 C 473,17 822,240 1920,94 V 0 H 0 V 133 Z">
                    <animate repeatCount="indefinite" attributeName="d" dur="15s" attributeType="XML"
                        values="
                            M0 133 C 473,17 822,240 1920,94 V 0 H 0 V 133 Z;
                            M0 133 C 473,240 1222,17 1920,84 V 0 H 0 V 133 Z;
                            M0 133 C 973,140 1722,300 1920,100 V 0 H 0 V 133 Z;
                            M0 133 C 473,17 822,240 1920,94 V 0 H 0 V 133 Z
                        ">
                    </animate>
                </path>
            </svg>
        </div>
    </div>
</div>
