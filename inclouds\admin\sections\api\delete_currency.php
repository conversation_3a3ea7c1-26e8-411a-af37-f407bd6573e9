<?php
require '../../../config/conn.php';

header('Content-Type: application/json');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit;
}

// التحقق من وجود معرف العملة
if (!isset($_POST['id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف العملة مطلوب']);
    exit;
}

// تنظيف وتحقق من البيانات
$id = intval($_POST['id']);

if ($id <= 0) {
    echo json_encode(['success' => false, 'message' => 'معرف العملة غير صحيح']);
    exit;
}

// التحقق من استخدام العملة في التبرعات قبل الحذف
$checkQuery = "SELECT COUNT(*) as count FROM donations WHERE currency = (SELECT symbol FROM currencies WHERE id = ?)";
$stmt = $conn->prepare($checkQuery);
$stmt->bind_param('i', $id);
$stmt->execute();
$result = $stmt->get_result();
$row = $result->fetch_assoc();

if ($row['count'] > 0) {
    echo json_encode(['success' => false, 'message' => 'لا يمكن حذف هذه العملة لأنها مستخدمة في التبرعات']);
    $stmt->close();
    exit;
}

// حذف العملة
$deleteQuery = "DELETE FROM currencies WHERE id = ?";
$stmt = $conn->prepare($deleteQuery);
$stmt->bind_param('i', $id);

if ($stmt->execute()) {
    echo json_encode(['success' => true, 'message' => 'تم حذف العملة بنجاح']);
} else {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء حذف العملة: ' . $conn->error]);
}

$stmt->close();
$conn->close();
