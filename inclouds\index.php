<?php

// جلب رقم الواتساب بشكل آمن
require_once 'config/conn.php';

if ($conn) {
    $query = "SELECT whatsapp FROM settings LIMIT 1";
    if ($result = mysqli_query($conn, $query)) {
        if (mysqli_num_rows($result) > 0) {
            $row = mysqli_fetch_assoc($result);
            // تنظيف الرقم (إزالة كل ما ليس رقمًا)
            $cleaned_number = preg_replace('/[^0-9]/', '', $row['whatsapp']);
            // التحقق من أن الرقم ليس فارغًا
            if (!empty($cleaned_number)) {
                $whatsapp_number = $cleaned_number;
            }
        }
        mysqli_free_result($result);
    }
    mysqli_close($conn);
}
?>


<!DOCTYPE html>
<html lang="ar" data-theme="light">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="موقع تبرعات خيري لدعم أهل غزة، تقديم المساعدات الإنسانية والعينية للعائلات المتضررة في قطاع غزة. تبرع الآن وساهم في إغاثة المتضررين">
    <meta name="keywords" content="تبرعات غزة, مساعدات إنسانية, دعم فلسطين, إغاثة غزة, صدقة, زكاة, مساعدة العائلات, تبرع لغزة, إعانة أيتام غزة">
    <meta name="robots" content="index, follow">
    <meta name="theme-color" content="#c3875d">
    <meta name="author" content="من الناس إلى الناس">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/assets/img/favicon.png">
    <link rel="apple-touch-icon" href="/assets/img/favicon.png">

    <!-- Open Graph / Social Media Meta Tags -->
    <meta property="og:title" content="من الناس إلى الناس - تبرعات وإغاثة لأهل غزة">
    <meta property="og:description" content="ساهم في إغاثة أهل غزة عبر التبرع لمشاريعنا الإنسانية. مساعدات غذائية، إيواء، كسوة وكفالات أيتام">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://nastonas.org">
    <meta property="og:image" content="https://nastonas.org/assets/img/favicon.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:locale" content="ar_AR">

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="من الناس إلى الناس - تبرعات لأهل غزة">
    <meta name="twitter:description" content="موقع تبرعات موثوق لدعم الأسر المتضررة في غزة">
    <meta name="twitter:image" content="https://nastonas.org/assets/img/favicon.png">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://nastonas.org">

    <title>من الناس إلى الناس - تبرعات وإغاثة لأهل غزة | مساعدات إنسانية</title>

    <!-- Structured Data -->
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "NGO",
            "name": "من الناس إلى الناس",
            "description": "مبادرة خيرية لتقديم المساعدات الإنسانية لأهل غزة",
            "url": "https://nastonas.org",
            "logo": "https://nastonas.org/assets/img/logo.png",
            "sameAs": [
                "https://facebook.com/nas2nasorg",
                "https://x.com/nas2nasorg?t=QL7IaMBpCdKC9z9ExncBvw&s=09",
                "https://instagram.com/nas2nasorg",
                "https://t.me/nas2nasorg",
                "https://youtube.com/@nas-to-nas?si=apNDLi7YG7JX1hst",
                "https://linkedin.com/company/nas2nasorg",
                "https://wa.me/970567573163"
            ],
            "potentialAction": {
                "@type": "DonateAction",
                "url": "https://nastonas.org/index#projects-section",
                "target": "https://nastonas.org/index#projects-section"
            }
        }
    </script>

    <style>
        body {
            scroll-behavior: smooth;
        }

        .rtl {
            direction: rtl;
        }
        @media (max-width: 768px) {
            .hero_section {
                gap: 50px;
            }
        }
    </style>
</head>

<body class="bg-white dark:bg-base-900">
    <?php require 'layout/splash.php' ?>
    <?php require 'layout/header.php' ?>
    <div class="hero_section flex flex-col p-8 md:flex-row justify-center"  style="background: #c3875d">
        <div class="w-full md:w-1/2">
            <?php require 'layout/slider.php' ?>
        </div>
        <div class="w-full md:w-1/2">
            <?php require 'layout/banner.php' ?>
        </div>
    </div>
    <?php require 'layout/boxs.php' ?>
    <?php require 'layout/projects-new.php' ?>
    <?php require 'layout/cash.php' ?>
    <?php require 'layout/footer.php' ?>


    <!-- زر الواتساب الثابت -->
    <a href="https://wa.me/<?php echo htmlspecialchars($whatsapp_number, ENT_QUOTES, 'UTF-8'); ?>" target="_blank" id="whatsapp-btn"
        class="btn btn-circle btn-success text-base-100 fixed bottom-8 right-8 z-50
          border border-white border-opacity-80  <!-- حد أبيض شبه شفاف -->
          hover:border-opacity-100  <!-- يصبح الحد أكثر وضوحاً عند التحويم -->
          transition-all duration-300
          shadow-md hover:shadow-lg  <!-- ظل خفيف -->
          hover:-translate-y-1">
        <i class="fab fa-whatsapp text-3xl" aria-hidden="true"></i>

    </a>

    <!-- زر العودة إلى الأعلى -->
    <button id="back-to-top" style="background:#c3875d"
        class="btn btn-circle fixed right-8 opacity-0 invisible
               transition-all duration-300 z-50
               bottom-8 hover:bottom-24">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
        </svg>
    </button>

    <script src="/assets/js/scroll_top.js" defer></script>

</body>

</html>