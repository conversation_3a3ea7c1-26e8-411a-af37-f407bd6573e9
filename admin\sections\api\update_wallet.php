<?php
include '../../../config/conn.php'; // تأكد من تضمين ملف الاتصال بقاعدة البيانات

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id = $_POST['id'];
    $address = $_POST['address'];

    // تأكد من أن $address ليست فارغة
    if (!empty($id) && !empty($address)) {
        // تحويل العناوين إلى مصفوفة
        $addressesArray = explode(',', $address); // تحويل السلسلة النصية إلى مصفوفة باستخدام الفواصل
        $jsonAddresses = json_encode(array_map('trim', $addressesArray)); // إزالة المسافات الزائدة وتحويل المصفوفة إلى JSON

        // استبدل العنوان القديم بالعنوان الجديد
        $query = "UPDATE wallet_addresses SET address = ? WHERE id = ?";
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, "si", $jsonAddresses, $id); // استخدام JSON هنا

        if (mysqli_stmt_execute($stmt)) {
            echo json_encode(["status" => "success", "message" => "تم تعديل العنوان بنجاح!"]);
        } else {
            echo json_encode(["status" => "error", "message" => "حدث خطأ أثناء تعديل العنوان."]);
        }
        mysqli_stmt_close($stmt);
    } else {
        echo json_encode(["status" => "error", "message" => "البيانات غير صحيحة."]);
    }
}

mysqli_close($conn);
?>
