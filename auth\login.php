<!DOCTYPE html>
<html lang="ar" data-theme="light">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- مكتبات -->
    <link rel="stylesheet" href="/assets/daisy/full.min.css">
    <link rel="stylesheet" href="/assets/tailwind/tailwind.min.css">
    <link rel="stylesheet" href="/assets/css/all.min.css">
    <link rel="stylesheet" href="/assets/font_cairo.css">
    <title>تسجيل الدخول</title>
    <style>
        * {
            box-sizing: border-box;
            padding: 0;
            margin: 0;
            font-family: cairo;
        }
    </style>
</head>

<body>

    <?php require '../layout/header.php' ?>

    <div dir="rtl" class="hero bg-base-200 mt-14 min-h-screen">
        <div class="hero-content flex-col w-full lg:flex-row-reverse">
            <div class="card bg-base-100 w-full max-w-md shrink-0 shadow-2xl px-4">

                <form id="login-form" class="card-body" method="POST">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">البريد الإلكتروني</span>
                        </label>
                        <input type="email" id="email" name="email" placeholder="البريد الإلكتروني" class="input input-bordered" required />
                    </div>
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">كلمة المرور</span>
                        </label>
                        <input type="password" id="password" name="password" placeholder="كلمة المرور" class="input input-bordered" required />
                    </div>
                    <div class="form-control mt-4">
                        <div class="inline-flex items-center gap-2">
                            <input type="checkbox" id="remember_me" name="remember_me" class="checkbox checkbox-sm checkbox-primary" />
                            <label for="remember_me" class="label-text cursor-pointer">تذكرني</label>
                        </div>
                    </div>
                    <div class="form-control mt-6">
                        <button class="btn btn-primary" type="submit">تسجيل الدخول</button>
                    </div>
                    <!--  انشاء حساب -->
                    <div class="form-control" style="display: flex; justify-content: center; align-items: center;">
                        <span class="label">
                            <a href="register">
                                <span class="text-blue-600 text-sm">إنشاء حساب جديد</span>
                            </a>
                        </span>
                    </div>


                </form>
            </div>
        </div>
    </div>

    <script defer>
        document.addEventListener('DOMContentLoaded', () => {
            const form = document.getElementById('login-form');
            const emailInput = document.getElementById('email'); 
            const passwordInput = document.getElementById('password');
            const rememberMeCheckbox = document.getElementById('remember_me');

            if (localStorage.getItem('rememberMe') === 'true') {
                emailInput.value = localStorage.getItem('email') || '';
                passwordInput.value = localStorage.getItem('password') || '';
                rememberMeCheckbox.checked = true;
            }

            form.addEventListener('submit', async (event) => {
                event.preventDefault();

                const formData = new FormData(form);

                try {
                    const response = await fetch('api/login.php', {
                        method: 'POST',
                        body: formData,
                        credentials: 'same-origin'
                    });

                    const result = await response.json();

                    if (result.status === 'success') {
                        alert(result.message);

                        if (rememberMeCheckbox.checked) {
                            localStorage.setItem('rememberMe', 'true');
                            localStorage.setItem('email', emailInput.value);
                            localStorage.setItem('password', passwordInput.value);
                        } else {
                            localStorage.removeItem('rememberMe');
                            localStorage.removeItem('email');
                            localStorage.removeItem('password');
                        }

                        // التوجيه بناءً على نوع المستخدم
                        window.location.href = result.role === 'admin' ? '../admin/dashboard' : '../user/dashboard';
                    } else {
                        alert('فشل تسجيل الدخول! ' + result.message);
                    }
                } catch (error) {
                    alert('حدث خطأ! يرجى المحاولة لاحقاً.');
                }
            });
        });
    </script>
</body>

</html>