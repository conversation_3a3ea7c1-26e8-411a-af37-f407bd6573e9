<!-- إضافة مكتبة Sortable.js -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

<style>
    /* أنماط السحب والإفلات المحسنة */
    .wallet-card {
        transition: all 0.3s ease;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        overflow: hidden;
        background-color: white;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-bottom: 1rem;
    }

    .wallet-card:hover {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .wallet-card.sortable-ghost {
        opacity: 0.4;
        background-color: #f3f4f6;
        border: 2px dashed #3b82f6;
    }

    .wallet-card.sortable-chosen {
        background-color: #f0f9ff;
        border-color: #3b82f6;
        box-shadow: 0 10px 15px rgba(59, 130, 246, 0.2);
    }

    .wallet-card.sortable-drag {
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        transform: rotate(1deg) scale(1.02);
    }

    .wallet-header {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
    }

    .wallet-body {
        padding: 1rem;
    }

    .wallet-footer {
        padding: 0.75rem 1rem;
        border-top: 1px solid #e5e7eb;
        background-color: #f9fafb;
    }

    .drag-handle {
        cursor: move;
        padding: 0.5rem;
        border-radius: 0.25rem;
        color: #6b7280;
        transition: all 0.2s ease;
    }

    .drag-handle:hover {
        color: #3b82f6;
        background-color: #e5e7eb;
    }

    .wallet-index {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        border-radius: 9999px;
        background-color: #3b82f6;
        color: white;
        font-weight: bold;
        margin-right: 0.75rem;
    }

    .animate-pulse {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.7;
        }
    }

    /* تحسينات للأزرار */
    .btn-modern {
        transition: all 0.3s ease;
        border-radius: 0.5rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 1rem;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .btn-modern:active {
        transform: translateY(0);
    }

    .btn-primary-modern {
        background-color: #3b82f6;
        color: white;
    }

    .btn-primary-modern:hover {
        background-color: #2563eb;
    }

    .btn-success-modern {
        background-color: #10b981;
        color: white;
    }

    .btn-success-modern:hover {
        background-color: #059669;
    }

    .btn-danger-modern {
        background-color: #ef4444;
        color: white;
    }

    .btn-danger-modern:hover {
        background-color: #dc2626;
    }

    .btn-accent-modern {
        background-color: #8b5cf6;
        color: white;
    }

    .btn-accent-modern:hover {
        background-color: #7c3aed;
    }

    /* تحسينات للمدخلات */
    .input-modern {
        border-radius: 0.5rem;
        border: 1px solid #e5e7eb;
        padding: 0.5rem 0.75rem;
        transition: all 0.3s ease;
    }

    .input-modern:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        outline: none;
    }
</style>

<div class="bg-white p-6 rounded-lg shadow-md mb-6">
    <div class="flex justify-between items-center mb-6">
        <div class="flex items-center">
            <div id="dragDropStatus" class="hidden bg-blue-100 text-blue-700 px-4 py-3 rounded-lg mr-4 shadow-sm">
                <i class="fas fa-info-circle mr-2"></i>
                <span>يمكنك سحب وإفلات وسائل الدفع لتغيير ترتيبها</span>
            </div>
            <button id="saveOrderBtn" class="hidden btn-modern btn-success-modern">
                <i class="fas fa-save mr-2"></i> حفظ الترتيب
            </button>
        </div>
        <button id="addWalletBtn" class="btn-modern btn-accent-modern">
            <i class="fas fa-plus-circle mr-2"></i> إضافة وسيلة دفع جديدة
        </button>
    </div>

    <div id="walletContainer" class="space-y-4">
        <!-- سيتم إضافة بطاقات وسائل الدفع هنا -->
    </div>
</div>

<script>
    window.onload = function() {
        loadWallets();
    };

    // تحميل بيانات المحافظ
    function loadWallets() {
        console.log('جاري تحميل وسائل الدفع...');

        // إظهار مؤشر التحميل
        const walletContainer = document.getElementById('walletContainer');
        walletContainer.innerHTML = `
            <div class="flex justify-center items-center p-8">
                <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                <span class="mr-4 text-gray-600">جاري تحميل وسائل الدفع...</span>
            </div>
        `;

        const xhr = new XMLHttpRequest();
        xhr.open('GET', '/admin/sections/api/get_wallets.php', true);

        xhr.onload = function() {
            if (xhr.status === 200) {
                try {
                    const wallets = JSON.parse(xhr.responseText);
                    walletContainer.innerHTML = '';

                    // ترتيب المحافظ حسب sort_order إذا كان موجودًا
                    wallets.sort((a, b) => {
                        const orderA = a.sort_order !== null ? parseInt(a.sort_order) : 999;
                        const orderB = b.sort_order !== null ? parseInt(b.sort_order) : 999;
                        return orderA - orderB;
                    });

                    // إذا لم تكن هناك وسائل دفع
                    if (wallets.length === 0) {
                        walletContainer.innerHTML = `
                            <div class="bg-gray-50 rounded-lg p-8 text-center">
                                <i class="fas fa-credit-card text-gray-300 text-5xl mb-4"></i>
                                <p class="text-gray-500 text-lg">لا توجد وسائل دفع حاليًا</p>
                                <button id="addFirstWalletBtn" class="btn-modern btn-accent-modern mt-4">
                                    <i class="fas fa-plus-circle mr-2"></i> إضافة وسيلة دفع جديدة
                                </button>
                            </div>
                        `;

                        document.getElementById('addFirstWalletBtn').addEventListener('click', function() {
                            document.getElementById('addWalletBtn').click();
                        });

                        return;
                    }

                    // إنشاء بطاقات وسائل الدفع
                    wallets.forEach((wallet, index) => {
                        const walletCard = document.createElement('div');
                        walletCard.className = 'wallet-card';
                        walletCard.setAttribute('data-id', wallet.id);

                        walletCard.innerHTML = `
                            <div class="wallet-header">
                                <div class="wallet-index">${index + 1}</div>
                                <div class="flex-1 font-bold text-lg">${wallet.method_name}</div>
                                <div class="drag-handle">
                                    <i class="fas fa-grip-vertical"></i>
                                </div>
                            </div>
                            <div class="wallet-body">
                                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <div class="md:col-span-1">
                                        <div class="bg-gray-50 p-2 rounded-lg flex items-center justify-center h-full">
                                            <img src="/assets/img/wallets/${wallet.method_image}" alt="${wallet.method_name}" class="w-20 h-20 object-contain" />
                                        </div>
                                    </div>
                                    <div class="md:col-span-3">
                                        <label class="block text-sm font-medium text-gray-700 mb-1">عناوين المحافظ:</label>
                                        <input type="text" value="${wallet.address}" data-id="${wallet.id}" class="input-modern w-full" />
                                    </div>
                                </div>
                            </div>
                            <div class="wallet-footer flex justify-end space-x-2">
                                <button onclick="updateWallet(${wallet.id})" class="btn-modern btn-primary-modern">
                                    <i class="fas fa-save mr-1"></i> تعديل
                                </button>
                                <button onclick="deleteWallet(${wallet.id})" class="btn-modern btn-danger-modern">
                                    <i class="fas fa-trash-alt mr-1"></i> حذف
                                </button>
                            </div>
                        `;

                        walletContainer.appendChild(walletCard);
                    });

                    // تفعيل ميزة السحب والإفلات باستخدام Sortable.js
                    initSortable();

                    // إظهار زر حفظ الترتيب وحالة السحب والإفلات
                    document.getElementById('dragDropStatus').classList.remove('hidden');
                    document.getElementById('saveOrderBtn').classList.remove('hidden');

                } catch (e) {
                    console.error("Parsing error:", e);
                    walletContainer.innerHTML = `
                        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                            <div class="flex">
                                <i class="fas fa-exclamation-circle text-xl mr-2"></i>
                                <div>
                                    <p class="font-bold">حدث خطأ في تحليل البيانات</p>
                                    <p class="text-sm">${e.message}</p>
                                </div>
                            </div>
                        </div>
                    `;
                }
            } else {
                console.error("Request failed:", xhr.statusText);
                walletContainer.innerHTML = `
                    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                        <div class="flex">
                            <i class="fas fa-exclamation-circle text-xl mr-2"></i>
                            <div>
                                <p class="font-bold">فشل في تحميل البيانات من الخادم</p>
                                <p class="text-sm">الرجاء المحاولة مرة أخرى لاحقًا</p>
                            </div>
                        </div>
                    </div>
                `;
            }
        };

        xhr.onerror = function() {
            console.error("Network error");
            walletContainer.innerHTML = `
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <div class="flex">
                        <i class="fas fa-exclamation-circle text-xl mr-2"></i>
                        <div>
                            <p class="font-bold">حدث خطأ في الاتصال بالخادم</p>
                            <p class="text-sm">الرجاء التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى</p>
                        </div>
                    </div>
                </div>
            `;
        };

        xhr.send();
    }

    // تحديث وسيلة الدفع
    function updateWallet(id) {
        const addressInput = document.querySelector(`input[data-id="${id}"]`);
        if (!addressInput) {
            console.error("Address input not found for ID:", id);
            return;
        }
        const newAddress = addressInput.value.split(',').map(item => item.trim()).join(',');

        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/admin/sections/api/update_wallet.php', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

        xhr.onload = function() {
            const responseMessage = document.getElementById('responseMessage');
            const response = JSON.parse(xhr.responseText);

            if (response.status === "success") {
                alert(response.message);
                loadWallets();
            } else {
                alert(response.message);
            }
        };

        xhr.send(`id=${id}&address=${encodeURIComponent(newAddress)}`);
    }

    // دالة الحذف
    function deleteWallet(id) {
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "لا يمكنك التراجع عن هذا الحذف!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'نعم، احذفها!',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                const xhr = new XMLHttpRequest();
                xhr.open('POST', '/admin/sections/api/delete_wallet.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

                xhr.onload = function() {
                    const response = JSON.parse(xhr.responseText);
                    if (response.status === "success") {
                        Swal.fire('تم الحذف!', response.message, 'success');
                        loadWallets();
                    } else {
                        Swal.fire('خطأ!', response.message, 'error');
                    }
                };

                xhr.send(`id=${id}`);
            }
        });
    }


    document.getElementById('addWalletBtn').addEventListener('click', function() {
        Swal.fire({
            title: 'إضافة وسيلة دفع جديدة',
            width: 800,
            html: `
        <div class="space-y-4">
            <input type="text" id="methodName" class="rtl w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="اسم وسيلة الدفع">
            <textarea id="walletAddresses" class="rtl w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="أدخل العناوين (افصل بين العناوين بفواصل)"></textarea>
            <input type="file" id="methodImage" class="rtl w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" accept="image/*" />
        </div>
    `,
            confirmButtonText: 'إضافة',
            confirmButtonColor: '#4CAF50',
            showCancelButton: true,
            cancelButtonText: 'إلغاء',
            cancelButtonColor: '#f44336',
            customClass: {
                popup: 'rounded-lg shadow-xl p-6 bg-white',
                title: 'text-xl font-semibold text-gray-700 mb-4',
                input: 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500',
                confirmButton: 'px-6 py-2 rounded-lg bg-green-600 text-white font-medium',
                cancelButton: 'px-6 py-2 rounded-lg bg-red-600 text-white font-medium',
            },
            preConfirm: () => {
                const methodName = document.getElementById('methodName').value;
                const walletAddresses = document.getElementById('walletAddresses').value;
                const methodImage = document.getElementById('methodImage').files[0];

                if (!methodName || !walletAddresses || !methodImage) {
                    Swal.showValidationMessage('يرجى ملء جميع الحقول');
                    return false;
                }

                // تحويل العناوين إلى مصفوفة
                const addressArray = walletAddresses.split(',').map(address => address.trim());

                if (addressArray.length === 0) {
                    Swal.showValidationMessage('يجب إدخال عنوان واحد على الأقل');
                    return false;
                }

                return {
                    methodName,
                    addressArray,
                    methodImage
                };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                const {
                    methodName,
                    addressArray,
                    methodImage
                } = result.value;

                // تحويل المصفوفة إلى JSON
                const addressJSON = JSON.stringify(addressArray);

                const formData = new FormData();
                formData.append('method_name', methodName);
                formData.append('address', addressJSON);
                formData.append('method_image', methodImage);

                const xhr = new XMLHttpRequest();
                xhr.open('POST', '/admin/sections/api/add_wallet.php', true);

                xhr.onload = function() {
                    const response = JSON.parse(xhr.responseText);
                    if (response.status === "success") {
                        Swal.fire('تمت الإضافة!', response.message, 'success');
                        loadWallets();
                    } else {
                        Swal.fire('خطأ!', response.message, 'error');
                    }
                };

                xhr.send(formData);
            }
        });
    });

    // تهيئة ميزة السحب والإفلات باستخدام Sortable.js
    function initSortable() {
        const walletContainer = document.getElementById('walletContainer');

        // إنشاء كائن Sortable جديد
        const sortable = new Sortable(walletContainer, {
            animation: 150, // مدة الرسوم المتحركة بالمللي ثانية
            handle: '.drag-handle', // عنصر المقبض للسحب
            ghostClass: 'sortable-ghost', // فئة CSS للعنصر الشبح أثناء السحب
            chosenClass: 'sortable-chosen', // فئة CSS للعنصر المختار
            dragClass: 'sortable-drag', // فئة CSS للعنصر أثناء السحب

            // تحديث أرقام الصفوف بعد الترتيب
            onEnd: function(evt) {
                if (evt.oldIndex !== evt.newIndex) {
                    // تحديث أرقام الصفوف
                    updateWalletIndexes();

                    // إظهار زر حفظ الترتيب
                    document.getElementById('saveOrderBtn').classList.add('animate-pulse');
                }
            }
        });

        // تحديث أرقام الصفوف
        updateWalletIndexes();

        return sortable;
    }

    // تحديث أرقام الصفوف
    function updateWalletIndexes() {
        const walletCards = document.querySelectorAll('.wallet-card');
        walletCards.forEach((card, index) => {
            const indexElement = card.querySelector('.wallet-index');
            if (indexElement) {
                indexElement.textContent = index + 1;
            }
        });
    }

    // حفظ ترتيب وسائل الدفع
    document.getElementById('saveOrderBtn').addEventListener('click', function() {
        // إظهار مؤشر التحميل في الزر
        const saveBtn = document.getElementById('saveOrderBtn');
        const originalBtnContent = saveBtn.innerHTML;
        saveBtn.innerHTML = `<i class="fas fa-spinner fa-spin mr-2"></i> جاري الحفظ...`;
        saveBtn.disabled = true;

        const walletContainer = document.getElementById('walletContainer');
        const walletCards = Array.from(walletContainer.querySelectorAll('.wallet-card'));

        // إنشاء مصفوفة من معرفات وسائل الدفع بالترتيب الجديد
        const orderedIds = walletCards.map((card, index) => {
            return {
                id: card.getAttribute('data-id'),
                order: index + 1
            };
        });

        console.log('ترتيب وسائل الدفع الجديد:', orderedIds);

        // إرسال الترتيب الجديد إلى الخادم
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/admin/sections/api/update_wallet_order.php', true);
        xhr.setRequestHeader('Content-Type', 'application/json');

        // دالة لاستعادة حالة الزر الأصلية
        const resetButton = () => {
            saveBtn.innerHTML = originalBtnContent;
            saveBtn.disabled = false;
            saveBtn.classList.remove('animate-pulse');
        };

        // تعيين مهلة زمنية لاستعادة حالة الزر في حالة عدم استجابة الخادم
        const resetTimeout = setTimeout(() => {
            console.warn('تم تجاوز الوقت المحدد للاستجابة، استعادة حالة الزر');
            resetButton();
        }, 10000); // 10 ثوانٍ كحد أقصى

        xhr.onload = function() {
            // إلغاء المهلة الزمنية
            clearTimeout(resetTimeout);

            try {
                const response = JSON.parse(xhr.responseText);
                if (response.status === "success") {
                    // إظهار رسالة نجاح مع تأثير متحرك
                    Swal.fire({
                        title: 'تم الحفظ!',
                        text: 'تم حفظ ترتيب وسائل الدفع بنجاح',
                        icon: 'success',
                        confirmButtonText: 'حسنًا',
                        showClass: {
                            popup: 'animate__animated animate__fadeInDown'
                        },
                        hideClass: {
                            popup: 'animate__animated animate__fadeOutUp'
                        }
                    });

                    // إزالة تأثير النبض من زر الحفظ واستعادة حالته الأصلية
                    saveBtn.classList.remove('animate-pulse');
                    saveBtn.innerHTML = originalBtnContent;
                    saveBtn.disabled = false;

                    // إعادة تحميل وسائل الدفع
                    setTimeout(() => {
                        loadWallets();
                    }, 500);
                } else {
                    // إظهار رسالة خطأ
                    Swal.fire({
                        title: 'خطأ!',
                        text: response.message || 'حدث خطأ أثناء حفظ الترتيب',
                        icon: 'error',
                        confirmButtonText: 'حسنًا'
                    });

                    // استعادة حالة الزر الأصلية
                    resetButton();
                }
            } catch (e) {
                console.error("Parsing error:", e);

                // إظهار رسالة خطأ
                Swal.fire({
                    title: 'خطأ!',
                    text: 'حدث خطأ أثناء معالجة استجابة الخادم',
                    icon: 'error',
                    confirmButtonText: 'حسنًا'
                });

                // استعادة حالة الزر الأصلية
                resetButton();
            }
        };

        xhr.onerror = function() {
            // إظهار رسالة خطأ
            Swal.fire({
                title: 'خطأ!',
                text: 'حدث خطأ في الاتصال بالخادم',
                icon: 'error',
                confirmButtonText: 'حسنًا'
            });

            // استعادة حالة الزر الأصلية
            resetButton();
        };

        xhr.send(JSON.stringify(orderedIds));
    });
</script>