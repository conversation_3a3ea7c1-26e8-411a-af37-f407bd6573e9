<?php
// الاتصال بقاعدة البيانات
include '../../../config/conn.php'; // تأكد من تضمين ملف الاتصال بقاعدة البيانات

// التحقق من طريقة الطلب (POST)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // الحصول على البيانات المدخلة من خلال AJAX
    $methodName = isset($_POST['method_name']) ? trim($_POST['method_name']) : '';
    $address = isset($_POST['address']) ? $_POST['address'] : ''; // نحتفظ بالـ address كما هو
    $methodImage = isset($_FILES['method_image']) ? $_FILES['method_image'] : null; // الحصول على الصورة

    // التحقق من صحة المدخلات
    if (empty($methodName) || empty($address)) {
        echo json_encode(['status' => 'error', 'message' => 'الرجاء ملء جميع الحقول']);
        exit;
    }

    // تحميل الصورة
    if ($methodImage && $methodImage['error'] === 0) {
        $imageName = time() . "_" . basename($methodImage['name']);
        $uploadDir = "../../../assets/img/wallets/"; // المجلد الذي سيتم تحميل الصورة فيه
        $uploadFile = $uploadDir . $imageName;

        if (move_uploaded_file($methodImage['tmp_name'], $uploadFile)) {
            $methodImage = $imageName;
        } else {
            echo json_encode(['status' => 'error', 'message' => 'حدث خطأ أثناء تحميل الصورة']);
            exit;
        }
    } else {
        $methodImage = null; // إذا لم يتم تحميل صورة
    }

    // تأكد أن العنوان هو JSON صالح
    $addressArray = json_decode($address, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo json_encode(['status' => 'error', 'message' => 'عنوان المحفظة غير صحيح. تأكد من أن البيانات بتنسيق JSON']);
        exit;
    }

    // تجنب الهجمات مثل SQL Injection
    $methodName = mysqli_real_escape_string($conn, $methodName);
    $address = mysqli_real_escape_string($conn, $address); // حفظ الـ JSON في قاعدة البيانات

    // استعلام لإضافة وسيلة الدفع إلى قاعدة البيانات
    $query = "INSERT INTO wallet_addresses (method_name, address, method_image, status) VALUES ('$methodName', '$address', '$methodImage', 'active')";

    // تنفيذ الاستعلام
    if (mysqli_query($conn, $query)) {
        echo json_encode(['status' => 'success', 'message' => 'تم إضافة وسيلة الدفع بنجاح']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'حدث خطأ أثناء إضافة وسيلة الدفع']);
    }
} else {
    echo json_encode(['status' => 'error', 'message' => 'طريقة الطلب غير صحيحة']);
}
?>
