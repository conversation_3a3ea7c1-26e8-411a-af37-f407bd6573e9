RewriteEngine On

# قاعدة إعادة الكتابة لصفحة المقالات
RewriteRule ^blog/article/([0-9]+)/([^/]+)$ blog/article.php?id=$1 [L,QSA]

# قاعدة لإعادة الكتابة إلى ملف .php إذا كان لا يحتوي على امتداد
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^([^\.]+)$ $1.php [NC,L]

# قاعدة لإعادة التوجيه إلى 404.php إلا إذا كان المسار يشير إلى ملف أو دليل حقيقي
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ /404.php [L]

# قاعدة لمنع عرض محتوى المجلد
Options -Indexes

