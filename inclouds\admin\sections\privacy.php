<div class="bg-gradient-to-br from-blue-50 to-purple-50 flex justify-center items-center min-h-screen">
    <div class="card bg-base-100 shadow-2xl p-8 w-full max-w-md border border-gray-200 rounded-lg">
        <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">تغيير بيانات الحساب</h1>
        <form id="changeCredentialsForm" class="space-y-6">
            <!-- حقل البريد الإلكتروني -->
            <div>
                <label for="newEmail" class="block text-right text-sm font-medium mb-2 text-gray-700">البريد الإلكتروني الجديد</label>
                <input type="email" id="newEmail" name="newEmail" required
                    class="input input-bordered w-full text-right placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200"
                    placeholder="أدخل البريد الإلكتروني الجديد" />
            </div>
            <!-- حقل كلمة المرور الجديدة -->
            <div>
                <label for="newPassword" class="block text-right text-sm font-medium mb-2 text-gray-700">كلمة المرور الجديدة</label>
                <input type="password" id="newPassword" name="newPassword" required
                    class="input input-bordered w-full text-right placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200"
                    placeholder="أدخل كلمة المرور الجديدة" />
            </div>
            <!-- حقل تأكيد كلمة المرور -->
            <div>
                <label for="confirmPassword" class="block text-right text-sm font-medium mb-2 text-gray-700">تأكيد كلمة المرور</label>
                <input type="password" id="confirmPassword" name="confirmPassword" required
                    class="input input-bordered w-full text-right placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200"
                    placeholder="أكد كلمة المرور الجديدة" />
            </div>
            <!-- زر الحفظ -->
            <button type="submit" class="btn btn-primary w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-200">
                حفظ التغييرات
            </button>
        </form>
    </div>
</div>

<script>
    document.getElementById('changeCredentialsForm').addEventListener('submit', function(event) {
        event.preventDefault();

        const newEmail = document.getElementById('newEmail').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        if (newPassword !== confirmPassword) {
            alert('كلمة المرور غير متطابقة');
            return;
        }

        const formData = new FormData();
        formData.append('newEmail', newEmail);
        formData.append('newPassword', newPassword);

        fetch('/admin/sections/api/update_credentials.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم تحديث البيانات بنجاح');
                } else {
                    alert('حدث خطأ أثناء تحديث البيانات');
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
    });
</script>