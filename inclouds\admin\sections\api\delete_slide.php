<?php
include '../../../config/conn.php'; // تأكد من أن الاتصال بقاعدة البيانات موجود

// تعيين نوع المحتوى إلى JSON
header('Content-Type: application/json');

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $slide_id = $_POST['slide_id'];

    // التحقق من أن slide_id هو رقم صحيح لتجنب الثغرات الأمنية
    if (is_numeric($slide_id)) {
        // استعلام لجلب مسار الصورة
        $selectQuery = "SELECT image_url FROM slides WHERE id = ?";
        $stmtSelect = $conn->prepare($selectQuery);
        $stmtSelect->bind_param("i", $slide_id);
        $stmtSelect->execute();
        $result = $stmtSelect->get_result();

        // التحقق من وجود الشريحة
        if ($result->num_rows === 0) {
            echo json_encode(['status' => 'error', 'message' => 'الشريحة غير موجودة']);
            $conn->close();
            exit;
        }

        $row = $result->fetch_assoc();
        $image_url = $row['image_url'];
        $stmtSelect->close();

        // حذف الصورة من المجلد
        $imagePath = "../../../assets/img/slider_img/" . $image_url;
        if (file_exists($imagePath)) {
            unlink($imagePath); // حذف الصورة
        }

        // استعلام لحذف السجل من قاعدة البيانات
        $query = "DELETE FROM slides WHERE id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $slide_id);

        if ($stmt->execute()) {
            echo json_encode(['status' => 'success', 'message' => 'تم حذف الشريحة بنجاح']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'حدث خطأ أثناء حذف الشريحة: ' . $stmt->error]);
        }

        $stmt->close();
    } else {
        echo json_encode(['status' => 'error', 'message' => 'معرف الشريحة غير صالح']);
    }
} else {
    echo json_encode(['status' => 'error', 'message' => 'طريقة الطلب غير صالحة']);
}

$conn->close();
?>
