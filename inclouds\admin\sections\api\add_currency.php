<?php
require '../../../config/conn.php';

header('Content-Type: application/json');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit;
}

// التحقق من وجود البيانات المطلوبة
if (!isset($_POST['name_ar']) || !isset($_POST['symbol']) || !isset($_POST['value'])) {
    echo json_encode(['success' => false, 'message' => 'جميع الحقول مطلوبة']);
    exit;
}

// تنظيف وتحقق من البيانات
$name_ar = trim($_POST['name_ar']);
$symbol = trim($_POST['symbol']);
$value = floatval($_POST['value']);

if (empty($name_ar) || empty($symbol) || $value <= 0) {
    echo json_encode(['success' => false, 'message' => 'يرجى إدخال بيانات صحيحة']);
    exit;
}

// التحقق من عدم وجود رمز العملة مسبقاً
$checkQuery = "SELECT id FROM currencies WHERE symbol = ?";
$stmt = $conn->prepare($checkQuery);
$stmt->bind_param('s', $symbol);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    echo json_encode(['success' => false, 'message' => 'رمز العملة موجود بالفعل']);
    $stmt->close();
    exit;
}

// إضافة العملة الجديدة
$insertQuery = "INSERT INTO currencies (name_ar, symbol, value) VALUES (?, ?, ?)";
$stmt = $conn->prepare($insertQuery);
$stmt->bind_param('ssd', $name_ar, $symbol, $value);

if ($stmt->execute()) {
    echo json_encode(['success' => true, 'message' => 'تمت إضافة العملة بنجاح']);
} else {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء إضافة العملة: ' . $conn->error]);
}

$stmt->close();
$conn->close();
