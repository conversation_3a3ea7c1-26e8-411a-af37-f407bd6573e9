<?php
include '../../../config/conn.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['field']) && isset($_POST['value'])) {
    $field = mysqli_real_escape_string($conn, $_POST['field']);
    $value = mysqli_real_escape_string($conn, $_POST['value']);

    $query = "UPDATE settings SET $field = '$value' WHERE id = 1";
    if (mysqli_query($conn, $query)) {
        echo json_encode(["status" => "success", "message" => "تم تحديث $field بنجاح."]);
    } else {
        echo json_encode(["status" => "error", "message" => "فشل في تحديث $field."]);
    }
} else {
    echo json_encode(["status" => "error", "message" => "بيانات غير صحيحة."]);
}
?>
