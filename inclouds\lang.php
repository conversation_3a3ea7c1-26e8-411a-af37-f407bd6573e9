<?php
session_start();
session_regenerate_id();

// قائمة باللغات المتاحة
$available_langs = ['ar'];

// تعيين اللغة بناءً على إعدادات لغة الجهاز إذا لم تكن موجودة في الجلسة
if (!isset($_SESSION['lang'])) {
    // الحصول على رأس اللغة من الطلب
    if (isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
        $accept_lang = $_SERVER['HTTP_ACCEPT_LANGUAGE'];
        // استخراج أول لغة من رأس اللغة
        $user_lang = substr($accept_lang, 0, 2);

        // التحقق مما إذا كانت اللغة المتاحة في القائمة
        if (in_array($user_lang, $available_langs)) {
            $_SESSION['lang'] = $user_lang;
        } else {
            // تعيين اللغة الافتراضية إذا لم تكن اللغة موجودة
            $_SESSION['lang'] = 'en';
        }
    } else {
        // تعيين اللغة الافتراضية إذا لم يكن رأس اللغة موجودًا
        $_SESSION['lang'] = 'en';
    }
}

// تغيير اللغة إذا تم تحديدها في الرابط
if (isset($_GET['lang']) && !empty($_GET['lang'])) {
    if (in_array($_GET['lang'], $available_langs)) {
        $_SESSION['lang'] = $_GET['lang'];
    }
}

// تحديد مسار ملف اللغة بناءً على القيمة المخزنة في الجلسة
$lang_file = __DIR__ . '/lang/' . $_SESSION['lang'] . '.php';

// التحقق من وجود الملف قبل محاولة تضمينه
if (file_exists($lang_file)) {
    include $lang_file;
} else {
    // تعيين لغة افتراضية إذا كان الملف غير موجود
    include __DIR__ . '/lang/en.php';
}
?>
