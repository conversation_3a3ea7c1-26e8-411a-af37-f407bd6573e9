<?php
// استيراد ملف الاتصال بقاعدة البيانات
include '../../../config/conn.php';

// التحقق من وجود ID المقال في الطلب
if (isset($_POST['id'])) {
    $id = $_POST['id'];

    // إعداد استعلام لاسترجاع اسم الصورة من قاعدة البيانات
    $query = "SELECT image FROM articles WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $id); // "i" تعني أن المعامل هو عدد صحيح

    if ($stmt->execute()) {
        // الحصول على النتيجة
        $stmt->bind_result($image);
        $stmt->fetch();

        // إذا كان هناك صورة مرتبطة بالمقال
        if ($image) {
            // تحديد المسار الكامل للصورة في المجلد
            $imagePath = '../../../assets/img/blog/' . $image;

            // التحقق مما إذا كانت الصورة موجودة ثم حذفها
            if (file_exists($imagePath)) {
                unlink($imagePath); // حذف الصورة
            }
        }

        // إغلاق الاستعلام الأول بعد استخدامه
        $stmt->close();

        // الآن استعلام الحذف للمقال
        $query = "DELETE FROM articles WHERE id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $id); // "i" تعني أن المعامل هو عدد صحيح

        if ($stmt->execute()) {
            // في حال النجاح
            echo json_encode(['success' => true]);
        } else {
            // في حال الفشل
            echo json_encode(['success' => false]);
        }

        $stmt->close();
    } else {
        // في حال عدم العثور على المقال
        echo json_encode(['success' => false, 'message' => 'المقال غير موجود']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'المقال غير موجود']);
}
?>
