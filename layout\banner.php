<style>
    .rtl {
        direction: rtl;
        text-align: right;
    }

    .modern-circle {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        background: linear-gradient(135deg, #c3875d, #e7b88f);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .cta-badge {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        background-color: #764d2f;
        color: #fff;
        padding: 6px 12px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.95rem;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .cta-badge:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    }

    .cta-badge svg {
        width: 18px;
        height: 18px;
        fill: #fff;
    }

    .modern-circle:hover {
        transform: scale(1.1);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.25);
    }



    /* حركة صعود ونزول */
    @keyframes upDown {

        0%,
        100% {
            transform: translateY(0);
        }

        50% {
            transform: translateY(12px);
        }
    }

    .up-down-icon {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background: radial-gradient(circle at top, #e7b88f, #c3875d);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        animation: upDown 2.2s infinite ease-in-out;
    }

    .up-down-icon:hover {
        transform: scale(1.1);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.25);
    }

    .up-down-icon .icon {
        color: white;
        font-size: 1.5rem;
    }
</style>

<div class="rtl-text text-center px-4 md:px-6 flex items-center flex-row-reverse flex-col gap-8">
    <div class="para flex flex-col justify-between h-full animate__animated animate__fadeInLeft">
        <h1 class="text-4xl font-bold text-white mb-4 drop-shadow-lg rtl animate__animated animate__fadeIn animate__delay-1s">
            نستقبل تبرعاتكم وزكواتكم
        </h1>
        <p
            class="text-base md:text-lg text-white leading-relaxed mx-auto rtl flex-grow
         animate__animated animate__fadeIn animate__delay-2s
         max-w-prose">
            نحن فريق تطوعي يعمل على الأرض داخل القطاع المحاصر، استنهضتنا ويلات الحرب
            لنساعد أهلنا المستضعفين. فقررنا أخذ زمام المبادرة، وتسخير خبراتنا ومهاراتنا
            الرقمية لتسهيل استقبال المساعدات المالية من الناس خارج غزة إلى الناس داخلها،
            عبر قنوات وبوابات دفع إلكتروني آمن.
        </p>

    </div>

    <!-- badge مع أيقونة SVG -->
    <div class="animate__animated animate__fadeIn animate__delay-3s">
        <span class="cta-badge">
            <!-- أيقونة قلب على سبيل المثال -->
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 
                   4.42 3 7.5 3c1.74 0 3.41 0.81 4.5 2.09C13.09 
                   3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 
                   8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
            </svg>
            <span>معاً من أجل غزة</span>
        </span>
    </div>

    <!-- المحتوى السابق -->
    <div class="flex items-center justify-center">
        <div class="up-down-icon">
            <i class="fas fa-angle-double-down icon"></i>
        </div>
    </div>
</div>