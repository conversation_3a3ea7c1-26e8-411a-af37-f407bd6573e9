<?php
// استيراد ملف الاتصال بقاعدة البيانات
include '../../../config/conn.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = $_POST['title'];
    $content = $_POST['content'];
    $views = $_POST['views'];  // عدد المشاهدات
    $image = $_FILES['image']['name'];  // اسم الصورة المرفوعة

    // توليد اسم عشوائي للصورة باستخدام uniqid()
    $imageExtension = strtolower(pathinfo($image, PATHINFO_EXTENSION)); // الحصول على امتداد الصورة وتحويله إلى أحرف صغيرة
    $randomImageName = uniqid('img_', true) . '.' . $imageExtension;  // توليد اسم فريد للصورة

    // قائمة الامتدادات المسموح بها
    $allowedExtensions = ['png', 'jpg', 'jpeg', 'gif'];

    // تحقق من امتداد الصورة
    if (!in_array($imageExtension, $allowedExtensions)) {
        echo json_encode(['success' => false, 'message' => 'نوع الملف غير مدعوم.']);
        exit;
    } else {
        // حفظ الصورة في المجلد المناسب
        $targetDir = "../../../assets/img/blog/"; // التأكد من وجود مجلد "uploads"

        // تحقق من وجود المجلد وإنشاءه إذا لم يكن موجودًا
        if (!is_dir($targetDir)) {
            mkdir($targetDir, 0777, true); // إنشاء المجلد إذا لم يكن موجودًا
        }

        $targetFile = $targetDir . $randomImageName;

        // تحقق من رفع الصورة بنجاح
        if (move_uploaded_file($_FILES["image"]["tmp_name"], $targetFile)) {
            // إدخال البيانات في قاعدة البيانات
            $status = 'published'; // كلمة ثابتة لقيمة status
            $stmt = $conn->prepare("INSERT INTO articles (title, content, status, views, image) VALUES (?, ?, ?, ?, ?)");
            $stmt->bind_param("sssds", $title, $content, $status, $views, $randomImageName);

            // تنفيذ الاستعلام
            if ($stmt->execute()) {
                // استرجاع جميع المقالات المضافة
                $result = $conn->query("SELECT * FROM articles ORDER BY created_at DESC");

                $articlesHTML = '';
                while ($row = $result->fetch_assoc()) {
                    $articlesHTML .= '<div class="bg-white shadow-lg rounded-lg overflow-hidden">';
                    $articlesHTML .= '<img src="../assets/img/blog/' . $row['image'] . '" alt="' . $row['title'] . '" class="w-full h-48 object-cover">';
                    $articlesHTML .= '<div class="p-4">';
                    $articlesHTML .= '<h3 class="text-lg font-bold text-gray-800">' . $row['title'] . '</h3>';
                    $articlesHTML .= '<p class="text-gray-600">' . $row['content'] . '</p>';

                    $articlesHTML .= '<div class="flex justify-between items-center mt-4">';
                    $articlesHTML .= '<span class="text-sm text-gray-500">عدد المشاهدات: ' . $row['views'] . '</span>';
                    $articlesHTML .= '</div>';
                    $articlesHTML .= '</div></div>';
                }

                echo json_encode(['success' => true, 'articles' => $articlesHTML]);
            } else {
                echo json_encode(['success' => false, 'message' => 'حدث خطأ في إضافة المقال.']);
            }

            $stmt->close();
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في رفع الصورة.']);
        }
    }
}
?>
