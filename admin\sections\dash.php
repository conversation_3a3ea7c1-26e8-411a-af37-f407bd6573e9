<style>
    .pie-chart {
        position: relative;
        width: 150px;
        height: 150px;
        border-radius: 50%;
        background: conic-gradient();
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
    }

    .pie-chart .percentage-text {
        position: absolute;
        font-size: 1.5rem;
        font-weight: bold;
        color: #4b5563;
    }

    .country-info {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin-top: 10px;
        text-align: center;
        font-size: 1rem;
        color: #4b5563;
    }

    .country-info span {
        display: block;
        margin: 5px 0;
        padding: 0 10px;
        border-right: 4px solid transparent;
        transition: border-color 0.3s ease, background-color 0.3s ease;
    }

    .country-info span:hover {
        background-color: rgba(0, 0, 0, 0.1);
    }


    .country-info .country-name {
        font-weight: bold;
    }

    @media (max-width: 768px),
    (max-width: 480px) {
        .stats {
            display: flex;
            flex-direction: column;
            width: 100%;
        }
    }
</style>

<div class="bg-gray-100 min-h-screen p-4">
    <div class=class="stats shadow-lg w-full mb-8 rounded-lg">
        <!-- إجمالي التبرعات حسب العملة -->
        <div class="w-full mb-8">
            <?php
            // جلب رموز العملات من جدول العملات
            $currenciesQuery = "SELECT symbol FROM currencies ORDER BY id ASC";
            $currenciesResult = mysqli_query($conn, $currenciesQuery);
            $currencies = [];
            while ($row = mysqli_fetch_assoc($currenciesResult)) {
                $currencies[] = $row['symbol'];
            }

            // إذا لم يتم العثور على عملات في الجدول، استخدم القيم الافتراضية
            if (empty($currencies)) {
                $currencies = ["USD", "EGP", "JOD", "EUR"];
            }

            $query = "SELECT currency, SUM(amount) AS total_amount FROM donations GROUP BY currency";
            $result = mysqli_query($conn, $query);

            // دالة لاختصار الأرقام
            function formatNumber($number)
            {
                if ($number >= 1000000) {
                    return round($number / 1000000, 1) . 'M';
                } elseif ($number >= 1000) {
                    return round($number / 1000, 1) . 'K';
                }
                return number_format($number, 2);
            }

            // جمع البيانات في مصفوفة
            $currencyData = [];
            while ($row = mysqli_fetch_assoc($result)) {
                if (in_array($row['currency'], $currencies)) {
                    $currencyData[] = $row;
                }
            }

            // تقسيم البيانات إلى صفوف (كل صف يحتوي على 3 عملات كحد أقصى)
            $chunkedData = array_chunk($currencyData, 3);

            // عرض البيانات في صفوف
            foreach ($chunkedData as $rowData) {
                echo '<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">';

                foreach ($rowData as $item) {
                    $formattedAmount = formatNumber($item['total_amount']);
                    $fullAmount = number_format($item['total_amount'], 2);

                    echo "
                    <div class='bg-white shadow-md hover:shadow-xl transition duration-300 ease-in-out p-6 rounded-lg flex items-center space-x-4'>
                        <div class='text-primary'>
                            <i class='text-6xl md:text-5xl text-green-500 drop-shadow-md'>{$item['currency']}</i>
                        </div>
                        <div class='flex-1 mr-4'>
                            <div class='text-lg font-medium text-gray-600 rtl'>إجمالي مبالغ التبرعات - {$item['currency']}</div>
                            <div class='text-2xl md:text-3xl text-gray-800 rtl font-bold' title='{$fullAmount}'>
                                {$formattedAmount}
                            </div>
                        </div>
                    </div>
                    ";
                }

                echo '</div>';
            }
            ?>
        </div>

        <!-- عدد المتبرعين والرسم البياني الدائري -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-8">
            <!-- عدد المتبرعين -->
            <div class="stat rtl bg-white rounded-xl shadow-md hover:shadow-xl transition duration-300 ease-in-out p-6">
                <div class="stat-title text-lg font-medium text-gray-600 rtl">عدد الداعمين</div>
                <div class="stat-value md:text-6xl text-gray-800 rtl" style="font-size: 50px;">
                    <?php
                    $donorsQuery = "SELECT COUNT(DISTINCT id) AS total_donors FROM donations";
                    $donorsResult = mysqli_query($conn, $donorsQuery);
                    $donorsRow = mysqli_fetch_assoc($donorsResult);
                    echo $donorsRow['total_donors'];
                    ?>
                </div>
                <div class="stat-figure text-primary">
                    <i class="fas fa-donate text-6xl md:text-5xl text-green-500 drop-shadow-md"></i>
                </div>
                <div class="stat-desc text-md text-gray-500 rtl">عدد الداعمين المجموع</div>
            </div>

            <!-- الرسم البياني الدائري -->
            <div class="bg-white rounded-xl shadow-md hover:shadow-xl transition duration-300 ease-in-out p-6">
                <div class="stat-title text-lg font-medium text-gray-600 rtl">أكثر 5 بلدان تبرعاً</div>
                <div class="charts flex justify-between">
                    <div class="stat-title text-lg font-medium text-gray-600 rtl">
                        <div class="pie-chart" id="pieChart"></div>
                    </div>
                    <div class="stat-figure text-primary flex">
                        <div class="country-info rtl" id="countryInfo"></div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function() {
        const countryData = <?php
                            $countryQuery = "SELECT country, SUM(amount) AS total_amount FROM donations GROUP BY country ORDER BY total_amount DESC LIMIT 5";
                            $countryResult = mysqli_query($conn, $countryQuery);
                            $countries = [];
                            $totalAmountQuery = "SELECT SUM(amount) AS total_amount FROM donations";
                            $totalAmountResult = mysqli_query($conn, $totalAmountQuery);
                            $totalRow = mysqli_fetch_assoc($totalAmountResult);
                            $totalAmount = $totalRow['total_amount'];
                            while ($row = mysqli_fetch_assoc($countryResult)) {
                                $percentage = ($row['total_amount'] / $totalAmount) * 100;
                                if ($percentage > 0) {
                                    $countries[] = ['country' => $row['country'], 'percentage' => $percentage, 'amount' => $row['total_amount']];
                                }
                            }
                            echo json_encode($countries);
                            ?>;

        const pieChart = document.getElementById('pieChart');
        const countryInfo = document.getElementById('countryInfo');

        let gradientValues = [];
        let totalPercentage = 0;
        const colors = ['#34d399', '#3b82f6', '#f59e0b', '#ef4444', '#f97316', '#9333ea'];

        countryData.forEach((data, index) => {
            const percentage = data.percentage;
            gradientValues.push(`${colors[index % colors.length]} ${totalPercentage}% ${totalPercentage + percentage}%`);
            totalPercentage += percentage;

            const countryElement = document.createElement('span');
            countryElement.classList.add('country-name');
            countryElement.innerText = `${data.country} - ${data.percentage.toFixed(2)}%`;

            countryElement.style.borderRight = `4px solid ${colors[index % colors.length]}`;

            countryInfo.appendChild(countryElement);
        });

        pieChart.style.background = `conic-gradient(${gradientValues.join(', ')})`;
    });
</script>