<link rel="stylesheet" href="/assets/siwper/swiper-bundle.min.css">
<script src="/assets/siwper/swiper-bundle.min.js" defer></script>

<style>
    .swiper-button-next,
    .swiper-button-prev {
        color: #ff5733 !important;
    }

    .swiper-pagination-bullet-active {
        background-color: #ff5733 !important;
    }

    /* تعديل الشريحة على الشاشات المختلفة */
    @media (min-width: 768px) {
        .slider-container {
            width: 100%;
            padding: 10px;
        }

        .swiper-container {
            border-radius: 8px;
            /* box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); */
        }
    }

    /* تحسين عرض الصور */
    .swiper-slide-img {
        width: 100%;
        height: auto;
        object-fit: contain;
        display: block;
        max-height: 400px; /* ارتفاع أقصى للصورة */
        border-radius: 8px;
    }

    .swiper-slide {
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
    }

    .swiper-container {
        height: auto;
        max-height: 400px; /* ارتفاع أقصى للحاوية */
        margin: 0 auto;
    }

    /* تحسين الاستجابة للشاشات المختلفة */
    @media (max-width: 768px) {
        .swiper-slide-img {
            max-height: 300px;
        }

        .swiper-container {
            max-height: 300px;
        }
    }
</style>

<!-- حاوية خارجية للشريحة مع عرض أصغر على الشاشات الكبيرة -->
<div class="slider-container w-full">
    <div class="swiper-container relative overflow-hidden">
        <div class="swiper-wrapper">
            <?php

            include('./config/conn.php');

            $result = $conn->query("SELECT * FROM slides");

            // عرض كل شريحة
            if ($result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    echo '
                <div class="swiper-slide relative">
                    <img src="../assets/img/slider_img/' . htmlspecialchars($row["image_url"]) . '" alt="' . htmlspecialchars($row["title"]) . '" class="swiper-slide-img">
                    <div class="absolute bottom-5 left-1/2 transform -translate-x-1/2 text-center max-w-sm p-4 rounded-lg flex flex-col space-y-2 bg-opacity-70 bg-black h-auto z-10">
                        <h2 class="text-xl md:text-2xl font-bold my-1 text-white">' . htmlspecialchars($row["title"]) . '</h2>
                        <p class="text-sm md:text-base my-1 text-white">' . htmlspecialchars($row["description"]) . '</p>';

                    if (!empty($row["link_url"])) {
                        echo '
                    <div class="flex justify-center">
                        <a href="' . htmlspecialchars($row["link_url"]) . '" class="text-l px-3 py-2 text-white rounded transition duration-300" style="background-color: #c3875d; display: inline-block; max-width: fit-content;">اقرأ المزيد</a>
                    </div>
                    ';
                    }

                    echo '
                    </div>
                </div>';
                }
            } else {
                echo "<p>لا توجد شرائح لعرضها.</p>";
            }

            $conn->close();
            ?>
        </div>

        <div class="swiper-pagination"></div>
        <!-- <div class="swiper-button-prev"></div>
        <div class="swiper-button-next"></div> -->
    </div>
</div>




<script>
    document.addEventListener("DOMContentLoaded", function() {
        const swiper = new Swiper(".swiper-container", {
            loop: true,
            speed: 1000,
            autoplay: {
                delay: 5000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            // إضافة خيارات لتحسين عرض الصور
            slidesPerView: 1,
            spaceBetween: 0,
            effect: "fade", // تأثير التلاشي بين الشرائح
            fadeEffect: {
                crossFade: true
            },
            // ضبط ارتفاع الشرائح
            autoHeight: true,
            // تحسين الاستجابة للشاشات المختلفة
            breakpoints: {
                // عندما يكون عرض النافذة >= 320px
                320: {
                    slidesPerView: 1,
                },
                // عندما يكون عرض النافذة >= 768px
                768: {
                    slidesPerView: 1,
                },
                // عندما يكون عرض النافذة >= 1024px
                1024: {
                    slidesPerView: 1,
                }
            }
        });
    });
</script>