<?php
header('Content-Type: application/json; charset=utf-8');

// استيراد ملف الاتصال بقاعدة البيانات
include '../../../config/conn.php';

// استعلام لجلب جميع المقالات
$query = "SELECT * FROM articles ORDER BY created_at DESC";
$result = $conn->query($query);

// التحقق من وجود بيانات
if ($result->num_rows > 0) {
    $articles = [];
    while ($row = $result->fetch_assoc()) {
        $articles[] = [
            'id' => $row['id'],
            'title' => $row['title'],
            'content' => $row['content'],
            'image' => $row['image'],
            'views' => $row['views'],
            'date' => $row['created_at'],

        ];
    }
    // إرسال المقالات كـ JSON
    echo json_encode(['success' => true, 'articles' => $articles]);
} else {
    // لا توجد مقالات
    echo json_encode(['success' => false, 'message' => 'لا توجد مقالات']);
}
?>
