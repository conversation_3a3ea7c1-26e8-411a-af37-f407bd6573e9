<?php
include('../../config/conn.php'); // تأكد من مسار ملف الاتصال بقاعدة البيانات

// هذا هو المسار الذي ستتصل به دالة AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);

    if (isset($input['method'])) {
        $method = $input['method'];

        // استعلام قاعدة البيانات بناءً على الطريقة
        $addresses = getWalletAddressesByMethod($method);

        if ($addresses) {
            echo json_encode([
                'success' => true,
                'addresses' => $addresses
            ]);
        } else {
            echo json_encode(['success' => false]);
        }
    }
    exit;
}

// دالة لجلب العناوين من قاعدة البيانات
function getWalletAddressesByMethod($method)
{
    global $conn;

    if ($conn->connect_error) {
        die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }

    $stmt = $conn->prepare('SELECT address FROM wallet_addresses WHERE method_name = ? AND status = "active"');
    $stmt->bind_param('s', $method);
    $stmt->execute();
    $result = $stmt->get_result();

    $addresses = [];
    while ($row = $result->fetch_assoc()) {
        $address = $row['address'];
        
        // التحقق إذا كان العنوان مخزنًا بصيغة JSON
        if ($decodedAddress = json_decode($address, true)) {
            // إذا كان JSON، نقوم بإنشاء عناصر <span> لكل عنوان
            foreach ($decodedAddress as $singleAddress) {
                $addresses[] = '<span class="text-green-700">' . htmlspecialchars($singleAddress) . '</span>';
            }
        } else {
            // في حال لم يكن JSON، ضف العنوان مباشرة كـ <span>
            $addresses[] = '<span class="text-green-700">' . htmlspecialchars($address) . '</span>';
        }
    }

    $stmt->close();
    return $addresses;
}
