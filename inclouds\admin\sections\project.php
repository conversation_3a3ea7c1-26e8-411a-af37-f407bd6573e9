<style>
    .rtl {
        direction: rtl;
        text-align: right;
    }
</style>

<div class="rtl max-w-6xl mx-auto bg-white shadow-lg rounded-lg p-8 animate__animated animate__fadeIn">
    <h1 class="text-3xl font-bold mb-8 text-center text-gray-800" id="form-title">إنشاء مشروع جديد</h1>

    <form id="create-article-form" enctype="multipart/form-data" class="space-y-8">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- القسم الأول: المعلومات الأساسية -->
            <div class="space-y-6 bg-white p-6 rounded-lg shadow-sm">
                <h2 class="text-xl font-semibold text-gray-700 border-b pb-2">المعلومات الأساسية</h2>

                <div class="form-control">
                    <label for="title" class="label font-semibold text-gray-700">عنوان المشروع:</label>
                    <input type="text" id="title" name="title" class="input input-bordered w-full focus:ring-2 focus:ring-blue-500 transition-all" required>
                </div>

                <div class="form-control">
                    <label for="desc" class="label font-semibold text-gray-700">وصف المشروع:</label>
                    <textarea id="desc" name="desc" class="textarea textarea-bordered w-full h-32 focus:ring-2 focus:ring-blue-500 transition-all" maxlength="500" required></textarea>
                </div>

                <div class="form-control">
                    <label for="image" class="label font-semibold text-gray-700">صورة المشروع:</label>
                    <input type="file" id="image" name="image" class="file-input file-input-bordered w-full focus:ring-2 focus:ring-blue-500 transition-all" accept="image/*">
                    <p class="text-xs text-gray-500 mt-1">يفضل صورة بأبعاد 800×600 بكسل</p>
                </div>
            </div>

            <!-- القسم الثاني: الإعدادات والمبالغ -->
            <div class="space-y-6 bg-white p-6 rounded-lg shadow-sm">
                <h2 class="text-xl font-semibold text-gray-700 border-b pb-2">إعدادات المشروع</h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- نوع المشروع -->
                    <div class="form-control bg-gray-50 p-3 rounded-lg">
                        <label for="project_type" class="label font-semibold text-gray-700">نوع المشروع:</label>
                        <select id="project_type" name="project_type" class="select select-bordered w-full focus:ring-2 focus:ring-blue-500 transition-all" required>
                            <option value="" disabled selected>اختر نوع المشروع</option>
                            <option value="always">مشروع دائم</option>
                            <option value="renewed">مشروع متجدد</option>
                        </select>
                    </div>

                    <!-- المبلغ المطلوب -->
                    <div class="form-control">
                        <label for="amount" class="label font-semibold text-gray-700">المبلغ المطلوب:</label>
                        <input type="number" id="amount" name="amount" class="input input-bordered w-full focus:ring-2 focus:ring-blue-500 transition-all ltr-input" required dir="ltr">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- كفالة الأيتام -->
                    <div class="form-control bg-gray-50 p-3 rounded-lg">
                        <label for="project_kafel" class="label font-semibold text-gray-700">كفالة الأيتام:</label>
                        <select id="project_kafel" name="project_kafel" class="select select-bordered w-full focus:ring-2 focus:ring-blue-500 transition-all" required>
                            <option value="no">لا</option>
                            <option value="yes">نعم</option>
                        </select>
                    </div>

                    <!-- مشروع عاجل -->
                    <div class="form-control bg-gray-50 p-3 rounded-lg">
                        <label for="is_urgent" class="label font-semibold text-gray-700">مشروع عاجل:</label>
                        <select id="is_urgent" name="is_urgent" class="select select-bordered w-full focus:ring-2 focus:ring-blue-500 transition-all" required>
                            <option value="no">لا</option>
                            <option value="yes">نعم</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div id="btn" class="mt-8">
            <button id="save_article" type="submit" class="btn btn-primary w-full md:w-1/2 mx-auto block text-lg py-3 animate__animated animate__pulse bg-gradient-to-r from-blue-500 to-blue-700 hover:from-blue-600 hover:to-blue-800 transition-all duration-300 shadow-lg">
                <i class="fas fa-plus-circle mr-2"></i> إنشاء مشروع جديد
            </button>
        </div>
    </form>

    <style>
        .ltr-input {
            direction: ltr;
            text-align: left;
        }

        /* تعديل موضع الأسهم في عناصر select لتظهر في اليسار */
        .select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: left 0.75rem center;
            background-size: 1rem;
            padding-left: 2.5rem !important;
            padding-right: 1rem !important;
        }

        /* تعديل موضع الأسهم في عناصر select في نافذة تعديل المشروع */
        .edit-project-modal .select {
            background-position: left 0.75rem center;
            padding-left: 2.5rem !important;
            padding-right: 1rem !important;
        }
    </style>


    <div id="all_project" class="all_project mt-12 bg-gray-50 p-6 rounded-lg shadow-md">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-gray-800">
                <i class="fas fa-project-diagram text-blue-500 mr-2"></i>
                جميع المشاريع
            </h2>
            <div class="flex gap-2">
                <div class="relative">
                    <input type="text" id="search-projects" placeholder="بحث في المشاريع..." class="input input-bordered w-full max-w-xs focus:ring-2 focus:ring-blue-500 transition-all pr-10">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
                <select id="filter-projects" class="select select-bordered focus:ring-2 focus:ring-blue-500 transition-all">
                    <option value="all">جميع المشاريع</option>
                    <option value="always">المشاريع الدائمة</option>
                    <option value="renewed">المشاريع المتجددة</option>
                    <option value="urgent">المشاريع العاجلة</option>
                </select>
            </div>
        </div>

        <div id="articles-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- المشاريع ستُضاف هنا عبر AJAX -->
        </div>

        <div id="no-projects-message" class="hidden text-center py-10">
            <i class="fas fa-folder-open text-gray-300 text-5xl mb-4"></i>
            <p class="text-gray-500 text-lg">لا توجد مشاريع متطابقة مع معايير البحث</p>
        </div>
    </div>

    <script>
        // إضافة وظائف البحث والتصفية
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search-projects');
            const filterSelect = document.getElementById('filter-projects');

            if (searchInput && filterSelect) {
                searchInput.addEventListener('input', filterProjects);
                filterSelect.addEventListener('change', filterProjects);

                function filterProjects() {
                    const searchTerm = searchInput.value.toLowerCase();
                    const filterValue = filterSelect.value;
                    const projectCards = document.querySelectorAll('#articles-container > div');
                    let visibleCount = 0;

                    console.log(`تصفية المشاريع: البحث="${searchTerm}", الفلتر="${filterValue}"`);

                    projectCards.forEach(card => {
                        const title = card.querySelector('h3')?.textContent.toLowerCase() || '';
                        const description = card.querySelector('p')?.textContent.toLowerCase() || '';
                        const projectType = card.getAttribute('data-project-type') || '';
                        const isUrgent = card.getAttribute('data-is-urgent') || 'no';

                        console.log(`بطاقة: نوع="${projectType}", عاجل="${isUrgent}"`);

                        const matchesSearch = title.includes(searchTerm) || description.includes(searchTerm);
                        let matchesFilter = false;

                        if (filterValue === 'all') {
                            matchesFilter = true;
                        } else if (filterValue === 'urgent') {
                            // تحقق من قيمة is_urgent بشكل صريح
                            matchesFilter = (isUrgent === 'yes');
                            console.log(`تصفية المشاريع العاجلة: ${isUrgent} => ${matchesFilter}`);
                        } else {
                            matchesFilter = filterValue === projectType;
                        }

                        console.log(`تطابق البحث: ${matchesSearch}, تطابق الفلتر: ${matchesFilter}`);

                        if (matchesSearch && matchesFilter) {
                            card.style.display = 'block';
                            visibleCount++;
                        } else {
                            card.style.display = 'none';
                        }
                    });

                    // إظهار رسالة عندما لا توجد نتائج
                    const noProjectsMessage = document.getElementById('no-projects-message');
                    if (noProjectsMessage) {
                        noProjectsMessage.style.display = visibleCount === 0 ? 'block' : 'none';
                    }

                    console.log(`عدد المشاريع المرئية: ${visibleCount}`);
                }
            }
        });
    </script>

</div>

<script defer>
    document.addEventListener("DOMContentLoaded", function() {

        const form = document.getElementById("create-article-form");

        form.addEventListener("submit", async function(event) {
            event.preventDefault();

            const submitButton = document.getElementById("save_article");
            submitButton.disabled = true;
            submitButton.textContent = "جاري الإرسال...";

            // جمع البيانات من الفورم
            const formData = new FormData(form);

            try {
                const response = await fetch("/admin/sections/api/save_project.php", {
                    method: "POST",
                    body: formData,
                });

                if (!response.ok) {
                    throw new Error("حدث خطأ أثناء الإرسال");
                }

                const result = await response.json();

                // تحقق من نجاح العملية
                if (result.success) {
                    submitButton.textContent = "تم انشاء المشروع";
                    loadProjects()
                    form.reset();
                } else {
                    alert("حدث خطأ: " + result.message);
                }
            } catch (error) {
                alert("حدث خطأ في الاتصال بالسيرفر.");
                console.error("Error:", error);
            } finally {
                submitButton.disabled = false;
                submitButton.textContent = "إنشاء مشروع جديد";
            }
        });

        function loadProjects() {
            console.log('جاري تحميل المشاريع...');
            const xhr = new XMLHttpRequest();
            xhr.open('GET', '/admin/sections/api/get_all_projects.php', true);
            xhr.onload = function() {
                if (xhr.status === 200) {
                    if (!xhr.responseText.trim()) {
                        console.error('استجابة فارغة من الخادم');
                        alert('استجابة فارغة من الخادم');
                        return;
                    }
                    try {
                        // طباعة الاستجابة للتصحيح
                        console.log('استجابة الخادم:', xhr.responseText.substring(0, 200) + '...');

                        const response = JSON.parse(xhr.responseText);
                        console.log('تم تحليل JSON بنجاح:', response);

                        if (response.success && Array.isArray(response.projects)) {
                            console.log(`تم العثور على ${response.projects.length} مشروع`);

                            // طباعة المشاريع العاجلة للتصحيح
                            const urgentProjects = response.projects.filter(p => p.is_urgent === 'yes');
                            console.log(`عدد المشاريع العاجلة: ${urgentProjects.length}`);
                            if (urgentProjects.length > 0) {
                                console.log('المشاريع العاجلة:', urgentProjects);
                            }

                            displayProjects(response.projects);

                            // تطبيق التصفية الحالية بعد تحميل المشاريع
                            const filterSelect = document.getElementById('filter-projects');
                            if (filterSelect && typeof filterProjects === 'function') {
                                setTimeout(filterProjects, 100); // تأخير قصير للتأكد من اكتمال عرض المشاريع
                            }
                        } else {
                            const project_container = document.getElementById('articles-container');
                            project_container.innerHTML = 'لا توجد مشاريع, قم بأنشاء مشروع جديد';
                            project_container.style.color = 'red';

                            // إظهار رسالة عدم وجود مشاريع
                            const noProjectsMessage = document.getElementById('no-projects-message');
                            if (noProjectsMessage) {
                                noProjectsMessage.style.display = 'block';
                            }
                        }
                    } catch (e) {
                        console.error('خطأ في تحليل JSON:', e);
                        console.error('النص المستلم:', xhr.responseText);
                        alert('خطأ في تحليل البيانات: ' + e.message);
                    }
                } else {
                    console.error('فشل الاتصال بالخادم، الحالة:', xhr.status);
                    alert('فشل الاتصال بالخادم');
                }
            };

            xhr.onerror = function() {
                console.error('حدث خطأ في الاتصال بالخادم');
                alert('حدث خطأ في الاتصال بالخادم');
            };

            xhr.send();
        }
        loadProjects();

        // دالة لعرض المشاريع
        function displayProjects(projects) {
            const container = document.getElementById('articles-container');
            container.innerHTML = '';

            if (projects.length === 0) {
                document.getElementById('no-projects-message').style.display = 'block';
                return;
            } else {
                document.getElementById('no-projects-message').style.display = 'none';
            }

            projects.forEach(project => {
                const projectElement = document.createElement('div');
                projectElement.classList.add('bg-white', 'shadow-lg', 'rounded-lg', 'overflow-hidden', 'animate__animated', 'animate__fadeIn', 'hover:shadow-xl', 'transition-all', 'duration-300');

                // إضافة سمات البيانات للتصفية
                projectElement.setAttribute('data-project-type', project.project_type);

                // تصحيح قيمة is_urgent وطباعتها للتصحيح
                const isUrgent = project.is_urgent === 'yes' ? 'yes' : 'no';
                projectElement.setAttribute('data-is-urgent', isUrgent);

                // طباعة معلومات المشروع للتصحيح
                console.log(`المشروع: ${project.title}, نوع: ${project.project_type}, عاجل: ${isUrgent}`);

                // تنسيق التاريخ مع التحقق من وجوده
                let formattedDate = '';
                try {
                    if (project.start_date) {
                        const dateObj = new Date(project.start_date);
                        formattedDate = dateObj.toLocaleDateString("ar-EG", {
                            day: "2-digit",
                            month: "2-digit",
                            year: "numeric"
                        });
                    } else if (project.created_at) {
                        const dateObj = new Date(project.created_at);
                        formattedDate = dateObj.toLocaleDateString("ar-EG", {
                            day: "2-digit",
                            month: "2-digit",
                            year: "numeric"
                        });
                    } else {
                        formattedDate = 'غير محدد';
                    }
                } catch (e) {
                    console.error('خطأ في تنسيق التاريخ:', e);
                    formattedDate = 'غير محدد';
                }

                // حساب نسبة التقدم
                const total = parseFloat(project.remaining_amount) || 0;
                const current = parseFloat(project.current_donations) || 0;
                const progressPercent = total > 0 ? Math.min(Math.round((current / total) * 100), 100) : 0;

                // تحديد لون الشريط بناءً على نوع المشروع
                const progressColor = project.is_urgent === 'yes' ? 'bg-red-500' : 'bg-blue-500';

                // تحديد شارة للمشروع العاجل
                const urgentBadge = project.is_urgent === 'yes' ?
                    `<div class="absolute top-0 right-0 bg-red-500 text-white px-3 py-1 rounded-bl-lg font-bold animate__animated animate__pulse animate__infinite">عاجل</div>` : '';

                projectElement.innerHTML = `
                <div class="relative">
                    <img src="../../../assets/img/project/${project.image_path}" alt="${project.title}" class="w-full h-48 object-cover">
                    ${urgentBadge}
                    <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2">
                        <div class="text-white text-xs">
                            تاريخ البدء: ${formattedDate}
                        </div>
                    </div>
                </div>
                <div class="p-5">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="text-lg font-bold text-gray-800">${project.title}</h3>
                        <span class="text-xs px-2 py-1 rounded-full ${project.project_type === 'renewed' ? 'bg-purple-100 text-purple-700' : 'bg-blue-100 text-blue-700'}">
                            ${project.project_type === 'renewed' ? 'متجدد' : 'دائم'}
                        </span>
                    </div>

                    <div class="mt-2 text-gray-600 text-sm">
                        <p>${project.description.substring(0, 100)}...</p>
                    </div>

                    <div class="mt-4">
                        <div class="flex justify-between text-sm mb-1">
                            <span>التقدم</span>
                            <span class="font-semibold ltr-input">${progressPercent}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="${progressColor} h-2 rounded-full" style="width: ${progressPercent}%"></div>
                        </div>
                        <div class="flex justify-between text-xs text-gray-500 mt-1 ltr-input">
                            <span>${current.toLocaleString()}</span>
                            <span>${total.toLocaleString()}</span>
                        </div>
                    </div>

                    <div class="flex items-center justify-between gap-3 w-full mt-4 pt-4 border-t">
                        <div>
                            ${project.project_kafel === 'yes' ?
                                '<span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">كفالة الأيتام</span>' : ''}
                        </div>
                        <div class="flex gap-2">
                            <a href="javascript:void(0);" class="btn btn-md btn-error text-center" id="delete-project-${project.id}">
                                <i class="fas fa-trash-alt"></i> حذف
                            </a>
                            <a href="javascript:void(0);" class="btn btn-md btn-primary text-center" id="edit-project-${project.id}">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                        </div>
                    </div>
                </div>
                `;

                container.appendChild(projectElement);

                document.getElementById(`delete-project-${project.id}`).addEventListener('click', function() {
                    delete_project(project.id);
                });
            });
        }


        function edit_project(projectId) {
            fetch(`/admin/sections/api/get_projects.php?id=${projectId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.project) {
                        const project = data.project;

                        Swal.fire({
                            title: 'تعديل المشروع',
                            width: '85%',
                            customClass: {
                                container: 'edit-project-modal',
                                popup: 'rounded-xl shadow-2xl border border-gray-100',
                                title: 'text-2xl font-bold text-gray-800 mb-6 border-b pb-3',
                                confirmButton: 'bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg transition-all shadow-md',
                                cancelButton: 'bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-lg transition-all shadow-md'
                            },
                            html: `
                    <form id="edit-project-form" class="rtl p-4 bg-white rounded-lg">
                        <!-- شريط التنقل بين الأقسام -->
                        <div class="flex flex-wrap justify-center mb-6 gap-2 border-b pb-4">
                            <button type="button" class="tab-btn active px-4 py-2 rounded-lg bg-blue-50 text-blue-600 font-semibold transition-all hover:bg-blue-100" data-tab="basic-info">
                                <i class="fas fa-info-circle ml-1"></i>المعلومات الأساسية
                            </button>
                            <button type="button" class="tab-btn px-4 py-2 rounded-lg bg-gray-100 text-gray-600 font-semibold transition-all hover:bg-gray-200" data-tab="project-settings">
                                <i class="fas fa-cog ml-1"></i>إعدادات المشروع
                            </button>
                            <button type="button" class="tab-btn px-4 py-2 rounded-lg bg-gray-100 text-gray-600 font-semibold transition-all hover:bg-gray-200" data-tab="financial-info">
                                <i class="fas fa-money-bill-wave ml-1"></i>المعلومات المالية
                            </button>
                        </div>

                        <!-- قسم المعلومات الأساسية -->
                        <div id="basic-info-tab" class="tab-content active">
                            <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                                <h2 class="text-xl font-semibold text-gray-700 mb-4 flex items-center">
                                    <i class="fas fa-info-circle text-blue-500 ml-2"></i>
                                    المعلومات الأساسية
                                </h2>

                                <div class="space-y-5">
                                    <div class="form-control">
                                        <label for="edit-title" class="label font-semibold text-gray-700 mb-1">عنوان المشروع:</label>
                                        <input type="text" id="edit-title" class="input input-bordered w-full focus:ring-2 focus:ring-blue-500 transition-all rounded-lg" value="${project.title}" required>
                                    </div>

                                    <div class="form-control">
                                        <label for="edit-desc" class="label font-semibold text-gray-700 mb-1">وصف المشروع:</label>
                                        <textarea id="edit-desc" class="textarea textarea-bordered w-full h-32 focus:ring-2 focus:ring-blue-500 transition-all rounded-lg" maxlength="500" required>${project.description}</textarea>
                                        <div class="text-xs text-gray-500 mt-1 flex justify-between">
                                            <span>يرجى وصف المشروع بشكل واضح ومختصر</span>
                                            <span id="char-count">0/500</span>
                                        </div>
                                    </div>

                                    <div class="form-control">
                                        <label for="edit-image" class="label font-semibold text-gray-700 mb-1">صورة المشروع:</label>
                                        <div class="flex flex-col md:flex-row items-start md:items-center gap-4">
                                            <div class="flex-grow w-full md:w-auto">
                                                <input type="file" id="edit-image" class="file-input file-input-bordered w-full focus:ring-2 focus:ring-blue-500 transition-all rounded-lg" accept="image/*">
                                                <p class="text-xs text-gray-500 mt-1">يفضل صورة بأبعاد 800×600 بكسل</p>
                                            </div>
                                            ${project.image_path ? `
                                            <div class="preview-container p-2 border rounded-lg bg-gray-50 flex items-center">
                                                <div class="w-20 h-20 overflow-hidden rounded-md">
                                                    <img src="../../../assets/img/project/${project.image_path}" class="w-full h-full object-cover" alt="صورة المشروع الحالية">
                                                </div>
                                                <div class="mr-3 text-sm text-gray-600">
                                                    <p>الصورة الحالية</p>
                                                    <p class="text-xs text-gray-500 truncate max-w-[150px]">${project.image_path}</p>
                                                </div>
                                            </div>` : ''}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- قسم إعدادات المشروع -->
                        <div id="project-settings-tab" class="tab-content hidden">
                            <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                                <h2 class="text-xl font-semibold text-gray-700 mb-4 flex items-center">
                                    <i class="fas fa-cog text-blue-500 ml-2"></i>
                                    إعدادات المشروع
                                </h2>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- نوع المشروع -->
                                    <div class="form-control bg-blue-50 p-4 rounded-xl">
                                        <label class="label font-semibold text-gray-700 mb-2">نوع المشروع:</label>
                                        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                                            <label class="flex items-center gap-2 cursor-pointer bg-white px-4 py-2 rounded-lg border border-gray-200 hover:border-blue-300 transition-all w-full sm:w-auto">
                                                <input type="radio" id="project_type_always" name="project_type" value="always" ${project.project_type === 'always' ? 'checked' : ''} class="radio radio-primary">
                                                <span>مشروع دائم</span>
                                            </label>
                                            <label class="flex items-center gap-2 cursor-pointer bg-white px-4 py-2 rounded-lg border border-gray-200 hover:border-blue-300 transition-all w-full sm:w-auto">
                                                <input type="radio" id="project_type_renewed" name="project_type" value="renewed" ${project.project_type === 'renewed' ? 'checked' : ''} class="radio radio-primary">
                                                <span>مشروع متجدد</span>
                                            </label>
                                        </div>
                                    </div>

                                    <!-- مشروع عاجل -->
                                    <div class="form-control bg-red-50 p-4 rounded-xl">
                                        <label class="label font-semibold text-gray-700 mb-2">مشروع عاجل:</label>
                                        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                                            <label class="flex items-center gap-2 cursor-pointer bg-white px-4 py-2 rounded-lg border border-gray-200 hover:border-red-300 transition-all w-full sm:w-auto">
                                                <input type="radio" id="is_urgent_no" name="is_urgent" value="no" ${project.is_urgent === 'no' || !project.is_urgent ? 'checked' : ''} class="radio radio-error">
                                                <span>لا</span>
                                            </label>
                                            <label class="flex items-center gap-2 cursor-pointer bg-white px-4 py-2 rounded-lg border border-gray-200 hover:border-red-300 transition-all w-full sm:w-auto">
                                                <input type="radio" id="is_urgent_yes" name="is_urgent" value="yes" ${project.is_urgent === 'yes' ? 'checked' : ''} class="radio radio-error">
                                                <span>نعم</span>
                                            </label>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-2">المشاريع العاجلة ستظهر في قسم المشاريع العاجلة في الصفحة الرئيسية</p>
                                    </div>

                                    <!-- كفالة الأيتام -->
                                    <div class="form-control bg-green-50 p-4 rounded-xl">
                                        <label class="label font-semibold text-gray-700 mb-2">كفالة الأيتام:</label>
                                        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                                            <label class="flex items-center gap-2 cursor-pointer bg-white px-4 py-2 rounded-lg border border-gray-200 hover:border-green-300 transition-all w-full sm:w-auto">
                                                <input type="radio" id="project_kafel_no" name="project_kafel" value="no" ${project.project_kafel === 'no' ? 'checked' : ''} class="radio radio-success">
                                                <span>لا</span>
                                            </label>
                                            <label class="flex items-center gap-2 cursor-pointer bg-white px-4 py-2 rounded-lg border border-gray-200 hover:border-green-300 transition-all w-full sm:w-auto">
                                                <input type="radio" id="project_kafel_yes" name="project_kafel" value="yes" ${project.project_kafel === 'yes' ? 'checked' : ''} class="radio radio-success">
                                                <span>نعم</span>
                                            </label>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-2">مشاريع كفالة الأيتام ستظهر بشارة خاصة في قائمة المشاريع</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- قسم المعلومات المالية -->
                        <div id="financial-info-tab" class="tab-content hidden">
                            <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                                <h2 class="text-xl font-semibold text-gray-700 mb-4 flex items-center">
                                    <i class="fas fa-money-bill-wave text-blue-500 ml-2"></i>
                                    المعلومات المالية
                                </h2>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="form-control bg-gray-50 p-4 rounded-xl">
                                        <label for="edit-amount" class="label font-semibold text-gray-700 mb-2">المبلغ المطلوب:</label>
                                        <div class="relative">
                                            <span class="absolute right-3 top-1/4 -translate-y-1/4 text-gray-500">$</span>
                                            <input type="number" id="edit-amount" class="input input-bordered w-full focus:ring-2 focus:ring-blue-500 transition-all ltr-input pr-8 rounded-lg" value="${project.amount}" required dir="ltr">
                                        </div>
                                        <p class="text-xs text-gray-500 mt-2">أدخل المبلغ الإجمالي المطلوب للمشروع</p>
                                    </div>

                                    <div class="form-control bg-gray-50 p-4 rounded-xl">
                                        <label for="edit-current_donations" class="label font-semibold text-gray-700 mb-2">تم تجميع حتى الآن:</label>
                                        <div class="relative">
                                            <span class="absolute right-3 top-1/4 -translate-y-1/4 text-gray-500">$</span>
                                            <input type="number" id="edit-current_donations" class="input input-bordered w-full focus:ring-2 focus:ring-blue-500 transition-all ltr-input pr-8 rounded-lg" value="${project.current_donations}" required dir="ltr">
                                        </div>
                                        <p class="text-xs text-gray-500 mt-2">أدخل المبلغ الذي تم جمعه حتى الآن</p>
                                    </div>
                                </div>

                                <!-- عرض شريط التقدم -->
                                <div class="mt-6 p-4 bg-white rounded-xl border border-gray-200">
                                    <h3 class="text-md font-semibold text-gray-700 mb-3">نسبة الإنجاز:</h3>
                                    <div class="flex justify-between text-sm mb-1">
                                        <span>التقدم</span>
                                        <span class="font-semibold ltr-input progress-percent">0%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-4">
                                        <div class="bg-blue-500 h-4 rounded-full progress-bar" style="width: 0%"></div>
                                    </div>
                                    <div class="flex justify-between text-xs text-gray-500 mt-1 ltr-input">
                                        <span class="current-amount">0</span>
                                        <span class="total-amount">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                    <style>
                        .ltr-input {
                            direction: ltr;
                            text-align: left;
                        }
                        .edit-project-modal .swal2-title {
                            font-size: 1.5rem;
                            color: #2d3748;
                        }
                        .edit-project-modal .swal2-html-container {
                            overflow-x: hidden;
                        }
                        .tab-btn.active {
                            background-color: #3b82f6;
                            color: white;
                        }
                        .tab-content {
                            display: none;
                        }
                        .tab-content.active {
                            display: block;
                        }
                        /* تعديل موضع الأسهم في عناصر select داخل النافذة المنبثقة */
                        .edit-project-modal .select,
                        .edit-project-modal select,
                        .edit-project-modal .file-select {
                            appearance: none;
                            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
                            background-repeat: no-repeat;
                            background-position: left 0.75rem center;
                            background-size: 1rem;
                            padding-left: 2.5rem !important;
                            padding-right: 1rem !important;
                        }
                        /* تعديل موضع الأسهم في عناصر file-input */
                        .edit-project-modal .file-input {
                            padding-left: 2.5rem !important;
                        }
                        @media (max-width: 640px) {
                            .tab-btn {
                                width: 100%;
                            }
                        }
                    </style>
                    `,
                            showCancelButton: true,
                            confirmButtonText: 'تحديث المشروع',
                            cancelButtonText: 'إلغاء',
                            didOpen: () => {
                                // Tab navigation functionality
                                const tabButtons = document.querySelectorAll('.tab-btn');
                                const tabContents = document.querySelectorAll('.tab-content');

                                tabButtons.forEach(button => {
                                    button.addEventListener('click', () => {
                                        // Remove active class from all buttons and contents
                                        tabButtons.forEach(btn => btn.classList.remove('active'));
                                        tabContents.forEach(content => content.classList.remove('active'));

                                        // Add active class to clicked button
                                        button.classList.add('active');

                                        // Show corresponding content
                                        const tabId = button.getAttribute('data-tab');
                                        document.getElementById(`${tabId}-tab`).classList.add('active');
                                    });
                                });

                                // Character count for description
                                const descTextarea = document.getElementById('edit-desc');
                                const charCount = document.getElementById('char-count');

                                function updateCharCount() {
                                    const currentLength = descTextarea.value.length;
                                    charCount.textContent = `${currentLength}/500`;

                                    // Change color if approaching limit
                                    if (currentLength > 450) {
                                        charCount.classList.add('text-red-500');
                                    } else {
                                        charCount.classList.remove('text-red-500');
                                    }
                                }

                                // Initial count
                                updateCharCount();

                                // Update on input
                                descTextarea.addEventListener('input', updateCharCount);

                                // Update progress bar when amount values change
                                const amountInput = document.getElementById('edit-amount');
                                const currentDonationsInput = document.getElementById('edit-current_donations');
                                const progressBar = document.querySelector('.progress-bar');
                                const progressPercent = document.querySelector('.progress-percent');
                                const currentAmount = document.querySelector('.current-amount');
                                const totalAmount = document.querySelector('.total-amount');

                                function updateProgressBar() {
                                    const total = parseFloat(amountInput.value) || 0;
                                    const current = parseFloat(currentDonationsInput.value) || 0;
                                    const percent = total > 0 ? Math.min(Math.round((current / total) * 100), 100) : 0;

                                    progressBar.style.width = `${percent}%`;
                                    progressPercent.textContent = `${percent}%`;

                                    // Format numbers with commas
                                    currentAmount.textContent = current.toLocaleString();
                                    totalAmount.textContent = total.toLocaleString();

                                    // Change color based on progress
                                    if (percent < 30) {
                                        progressBar.className = 'bg-red-500 h-4 rounded-full';
                                    } else if (percent < 70) {
                                        progressBar.className = 'bg-yellow-500 h-4 rounded-full';
                                    } else {
                                        progressBar.className = 'bg-green-500 h-4 rounded-full';
                                    }
                                }

                                // Initial update
                                updateProgressBar();

                                // Update on input
                                amountInput.addEventListener('input', updateProgressBar);
                                currentDonationsInput.addEventListener('input', updateProgressBar);

                                // Radio button event listeners
                                document.getElementById('project_type_always').addEventListener('change', function() {
                                    console.log('Selected project type:', this.checked ? 'always' : '');
                                });

                                document.getElementById('project_kafel_yes').addEventListener('change', function() {
                                    console.log('Selected project kafel:', this.checked ? 'yes' : '');
                                });

                                document.getElementById('is_urgent_yes').addEventListener('change', function() {
                                    console.log('Selected is urgent:', this.checked ? 'yes' : '');
                                });
                            },
                            preConfirm: () => {
                                // التحقق من صحة البيانات
                                const title = document.getElementById('edit-title').value;
                                const description = document.getElementById('edit-desc').value;
                                const amount = document.getElementById('edit-amount').value;
                                const current_donations = document.getElementById('edit-current_donations').value;

                                // التحقق من إدخال العنوان
                                if (!title.trim()) {
                                    Swal.showValidationMessage('يرجى إدخال عنوان المشروع');
                                    // تبديل إلى علامة التبويب المناسبة
                                    document.querySelector('[data-tab="basic-info"]').click();
                                    return false;
                                }

                                // التحقق من إدخال الوصف
                                if (!description.trim()) {
                                    Swal.showValidationMessage('يرجى إدخال وصف المشروع');
                                    document.querySelector('[data-tab="basic-info"]').click();
                                    return false;
                                }

                                // التحقق من إدخال المبلغ المطلوب
                                if (!amount || isNaN(amount) || parseFloat(amount) <= 0) {
                                    Swal.showValidationMessage('يرجى إدخال مبلغ صحيح للمشروع');
                                    document.querySelector('[data-tab="financial-info"]').click();
                                    return false;
                                }

                                // التحقق من إدخال المبلغ المجمع
                                if (!current_donations || isNaN(current_donations) || parseFloat(current_donations) < 0) {
                                    Swal.showValidationMessage('يرجى إدخال مبلغ صحيح للتبرعات الحالية');
                                    document.querySelector('[data-tab="financial-info"]').click();
                                    return false;
                                }

                                // جمع قيم الخيارات
                                const project_type = [];
                                if (document.getElementById('project_type_always').checked) project_type.push('always');
                                if (document.getElementById('project_type_renewed').checked) project_type.push('renewed');

                                // التحقق من اختيار نوع المشروع
                                if (project_type.length === 0) {
                                    Swal.showValidationMessage('يرجى اختيار نوع المشروع');
                                    document.querySelector('[data-tab="project-settings"]').click();
                                    return false;
                                }

                                const project_kafel = [];
                                if (document.getElementById('project_kafel_yes').checked) project_kafel.push('yes');
                                if (document.getElementById('project_kafel_no').checked) project_kafel.push('no');

                                // التحقق من اختيار كفالة الأيتام
                                if (project_kafel.length === 0) {
                                    Swal.showValidationMessage('يرجى تحديد ما إذا كان المشروع لكفالة الأيتام');
                                    document.querySelector('[data-tab="project-settings"]').click();
                                    return false;
                                }

                                const is_urgent = [];
                                if (document.getElementById('is_urgent_yes').checked) is_urgent.push('yes');
                                if (document.getElementById('is_urgent_no').checked) is_urgent.push('no');

                                // التحقق من اختيار حالة الاستعجال
                                if (is_urgent.length === 0) {
                                    Swal.showValidationMessage('يرجى تحديد ما إذا كان المشروع عاجلاً');
                                    document.querySelector('[data-tab="project-settings"]').click();
                                    return false;
                                }

                                // التعامل مع الصورة
                                const image = document.getElementById('edit-image').files[0];
                                const imageName = image ? image.name : project.image_path;

                                // التحقق من نوع الصورة إذا تم تحميلها
                                if (image) {
                                    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
                                    if (!allowedTypes.includes(image.type)) {
                                        Swal.showValidationMessage('يرجى اختيار ملف صورة صالح (JPG, PNG, GIF, WEBP)');
                                        document.querySelector('[data-tab="basic-info"]').click();
                                        return false;
                                    }

                                    // التحقق من حجم الصورة (أقل من 5 ميجابايت)
                                    if (image.size > 5 * 1024 * 1024) {
                                        Swal.showValidationMessage('حجم الصورة كبير جداً. يجب أن يكون أقل من 5 ميجابايت');
                                        document.querySelector('[data-tab="basic-info"]').click();
                                        return false;
                                    }
                                }

                                console.log('Form data:', {
                                    project_type: project_type.join(','),
                                    project_kafel: project_kafel.join(','),
                                    is_urgent: is_urgent.join(','),
                                    imageName
                                });

                                // إنشاء كائن FormData لإرسال البيانات
                                const formData = new FormData();
                                formData.append('id', projectId);
                                formData.append('title', title);
                                formData.append('description', description);
                                formData.append('amount', amount);
                                formData.append('current_donations', current_donations);
                                formData.append('project_type', project_type.join(','));
                                formData.append('project_kafel', project_kafel.join(','));
                                formData.append('is_urgent', is_urgent.join(','));
                                formData.append('image_name', imageName);
                                if (image) formData.append('image', image);

                                // عرض مؤشر التحميل
                                Swal.showLoading();

                                return fetch('/admin/sections/api/update_project.php', {
                                        method: 'POST',
                                        body: formData,
                                    })
                                    .then(response => {
                                        if (!response.ok) {
                                            throw new Error(`خطأ في الاستجابة: ${response.status}`);
                                        }
                                        return response.json();
                                    })
                                    .then(result => {
                                        if (result.success) {
                                            // إظهار رسالة نجاح متحركة
                                            Swal.fire({
                                                icon: 'success',
                                                title: 'تم تحديث المشروع بنجاح',
                                                text: 'تم حفظ جميع التغييرات بنجاح',
                                                confirmButtonText: 'حسناً',
                                                customClass: {
                                                    popup: 'animate__animated animate__fadeInUp'
                                                }
                                            });

                                            // تحديث قائمة المشاريع
                                            loadProjects();
                                        } else {
                                            // إظهار رسالة الخطأ
                                            Swal.fire({
                                                icon: 'error',
                                                title: 'فشل تحديث المشروع',
                                                text: result.message || 'حدث خطأ أثناء تحديث المشروع',
                                                confirmButtonText: 'حاول مرة أخرى',
                                                customClass: {
                                                    popup: 'animate__animated animate__shakeX'
                                                }
                                            });
                                        }
                                    })
                                    .catch(error => {
                                        console.error('Error updating project:', error);

                                        // إظهار رسالة خطأ الاتصال
                                        Swal.fire({
                                            icon: 'error',
                                            title: 'فشل الاتصال',
                                            text: 'حدث خطأ في الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.',
                                            confirmButtonText: 'حاول مرة أخرى',
                                            customClass: {
                                                popup: 'animate__animated animate__shakeX'
                                            }
                                        });
                                    });
                            }
                        });
                    } else {
                        Swal.fire('فشل في تحميل المشروع', 'لم يتم العثور على المشروع', 'error');
                    }
                })
                .catch(error => {
                    Swal.fire('حدث خطأ', 'فشل في جلب بيانات المشروع', 'error');
                });
        }


        document.getElementById('articles-container').addEventListener('click', function(event) {
            if (event.target && event.target.matches('a[id^="edit-project-"]')) {
                const projectId = event.target.id.split('-').pop();
                edit_project(projectId);
            }
        });

        // دالة لحذف المشروع
        function delete_project(projectId) {
            if (confirm('هل أنت متأكد من حذف هذا المشروع؟')) {
                const xhr = new XMLHttpRequest();
                xhr.open('POST', '/admin/sections/api/delete_project.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            alert('تم حذف المشروع بنجاح');
                            loadProjects();
                        } else {
                            alert('فشل في حذف المشروع');
                        }
                    } else {
                        alert('فشل الاتصال بالخادم');
                    }
                };

                xhr.send(`id=${projectId}`);
            }
        }


        const projectTypeSelect = document.getElementById('project_type');
        const durationContainer = document.getElementById('duration_container');

        projectTypeSelect.addEventListener('change', function() {
            if (projectTypeSelect.value === 'renewed') {
                durationContainer.style.display = 'block';
            } else {
                durationContainer.style.display = 'none';
            }
        });

    });
</script>