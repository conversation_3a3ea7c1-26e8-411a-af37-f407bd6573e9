<?php
session_start();

header('Content-Type: application/json');

require '../../config/conn.php'; 

$email = filter_var(trim($_POST['email']), FILTER_VALIDATE_EMAIL);
$password = $_POST['password'];

// التحقق من صحة المدخلات
if (empty($email) || empty($password)) {
    echo json_encode([
        'status' => 'error',
        'message' => 'برجاء ملئ جميع الحقول!'
    ]);
    exit;
}

// التحقق من صحة بيانات المستخدم
$query = "SELECT * FROM users WHERE `email` = ?";
$stmt = $conn->prepare($query);
if ($stmt === false) {
    echo json_encode([
        'status' => 'error',
        'message' => 'حدث خطأ في الاستعلام!'
    ]);
    exit;
}
$stmt->bind_param('s', $email);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo json_encode([
        'status' => 'error',
        'message' => 'البريد الإلكتروني غير موجود'
    ]);
    exit;
}

// التحقق من كلمة المرور
$user = $result->fetch_assoc();
if (password_verify($password, $user['password'])) {
    // توليد توكن عشوائي
    $token = bin2hex(random_bytes(32));

    // إعداد الجلسة مع تفعيل أمان الجلسة
    session_regenerate_id(true); // لتجنب الجلسات الثابتة
    $_SESSION['logged_in'] = true;
    $_SESSION['user_id'] = $user['user_id'];
    $_SESSION['user_role'] = $user['role'];
    $_SESSION['user_name'] = htmlspecialchars($user['name'], ENT_QUOTES, 'UTF-8');
    $_SESSION['token'] = $token;

    // تحديث قاعدة البيانات بالتوكن
    $update_query = "UPDATE users SET token = ? WHERE user_id = ?";
    $update_stmt = $conn->prepare($update_query);
    if ($update_stmt === false) {
        echo json_encode([
            'status' => 'error',
            'message' => 'فشل تحديث التوكن!'
        ]);
        exit;
    }
    $update_stmt->bind_param('si', $token, $user['user_id']);
    $update_stmt->execute();

    // تعيين التوكن في الكوكيز مع حماية HTTPOnly و Secure
    setcookie('token', $token, [
        'expires' => time() + 86400, 
        'path' => '/', 
        'secure' => true, // يتطلب الاتصال عبر HTTPS
        'httponly' => true, // يمنع JavaScript من الوصول إلى الكوكيز
        'samesite' => 'Strict' // يمنع إرسال الكوكيز مع الطلبات الخارجية
    ]);

    echo json_encode([
        'status' => 'success',
        'message' => 'تم تسجيل الدخول بنجاح!',
        'role' => $user['role'] // إرجاع نوع المستخدم
    ]);
} else {
    echo json_encode([
        'status' => 'error',
        'message' => 'كلمة المرور غير صحيحه'
    ]);
}

// إغلاق البيان
$stmt->close();
if (isset($update_stmt)) {
    $update_stmt->close();
}
$conn->close();
?>
