<?php
// الاتصال بقاعدة البيانات
include '../../../config/conn.php';

// تحديد الاستجابة الافتراضية
$response = ['success' => false, 'message' => 'فشل في حذف المشروع.'];

// التحقق من إرسال معرف المشروع
if (isset($_POST['id']) && !empty($_POST['id'])) {
    $projectId = (int)$_POST['id'];  // تحويل المعرف إلى عدد صحيح

    // استعلام لحذف المشروع بناءً على المعرف
    $query = "DELETE FROM projects WHERE id = $projectId";

    // تنفيذ الاستعلام
    if (mysqli_query($conn, $query)) {
        $response = [
            'success' => true,
            'message' => 'تم حذف المشروع بنجاح.'
        ];
    } else {
        $response = [
            'success' => false,
            'message' => 'حدث خطأ أثناء حذف المشروع: ' . mysqli_error($conn)
        ];
    }
} else {
    $response = ['success' => false, 'message' => 'معرف المشروع غير صالح.'];
}

// إرسال الاستجابة بتنسيق JSON
header('Content-Type: application/json');
echo json_encode($response);
?>
