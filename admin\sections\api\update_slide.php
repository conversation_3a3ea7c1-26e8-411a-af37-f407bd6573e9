<?php
include '../../../config/conn.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $slide_id = $_POST['slide_id'];
    $title = $_POST['title'];
    $description = $_POST['desc'];
    $link_url = $_POST['link_url'];
    $has_new_image = isset($_FILES['image']) && $_FILES['image']['size'] > 0;

    // التحقق من أن slide_id هو رقم صحيح لتجنب الثغرات الأمنية
    if (is_numeric($slide_id)) {
        // إذا تم تحميل صورة جديدة
        if ($has_new_image) {
            // استعلام لجلب مسار الصورة القديمة
            $selectQuery = "SELECT image_url FROM slides WHERE id = ?";
            $stmtSelect = $conn->prepare($selectQuery);
            $stmtSelect->bind_param("i", $slide_id);
            $stmtSelect->execute();
            $stmtSelect->bind_result($old_image_url);
            $stmtSelect->fetch();
            $stmtSelect->close();

            // التحقق من امتداد الصورة الجديدة
            $image = $_FILES['image']['name'];
            $imageExtension = pathinfo($image, PATHINFO_EXTENSION);
            $allowedExtensions = ['png', 'jpg', 'jpeg'];
            
            if (in_array(strtolower($imageExtension), $allowedExtensions)) {
                // توليد اسم عشوائي للصورة الجديدة
                $randomImageName = uniqid() . '.' . $imageExtension;
                $target = "../../../assets/img/slider_img/" . basename($randomImageName);

                if (move_uploaded_file($_FILES['image']['tmp_name'], $target)) {
                    // حذف الصورة القديمة
                    $oldImagePath = "../../../assets/img/slider_img/" . $old_image_url;
                    if (file_exists($oldImagePath)) {
                        unlink($oldImagePath);
                    }

                    // تحديث السجل في قاعدة البيانات مع الصورة الجديدة
                    $query = "UPDATE slides SET title = ?, description = ?, image_url = ?, link_url = ? WHERE id = ?";
                    $stmt = $conn->prepare($query);
                    $stmt->bind_param("ssssi", $title, $description, $randomImageName, $link_url, $slide_id);
                } else {
                    echo json_encode(['status' => 'error', 'message' => 'فشل في تحميل الصورة الجديدة']);
                    exit;
                }
            } else {
                echo json_encode(['status' => 'error', 'message' => 'امتداد الصورة غير مدعوم. يرجى تحميل صورة بصيغة PNG أو JPG أو JPEG']);
                exit;
            }
        } else {
            // تحديث السجل بدون تغيير الصورة
            $query = "UPDATE slides SET title = ?, description = ?, link_url = ? WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("sssi", $title, $description, $link_url, $slide_id);
        }

        if ($stmt->execute()) {
            echo json_encode(['status' => 'success', 'message' => 'تم تحديث الشريحة بنجاح']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'حدث خطأ أثناء تحديث الشريحة: ' . $stmt->error]);
        }

        $stmt->close();
    } else {
        echo json_encode(['status' => 'error', 'message' => 'معرف الشريحة غير صالح']);
    }
} else {
    echo json_encode(['status' => 'error', 'message' => 'طريقة الطلب غير صالحة']);
}

$conn->close();
?>
