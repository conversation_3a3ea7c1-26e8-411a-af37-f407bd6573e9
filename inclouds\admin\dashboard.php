<?php
session_start();
require '../config/conn.php';

// دالة لتحطيم الجلسة والتوكن
function destroySession()
{
    session_unset();
    session_destroy();
    setcookie('token', '', time() - 3600, '/', '', true, true);
    header('Location: ../auth/login.php');
    exit;
}

// دالة للتحقق من التوكن وتوجيه المستخدم
function checkAuth($conn)
{
    if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true || !isset($_COOKIE['token'])) {
        destroySession();
    }

    $token = $_COOKIE['token'];
    $user_id = $_SESSION['user_id'];

    $query = "SELECT role FROM users WHERE user_id = ? AND token = ?";
    $stmt = $conn->prepare($query);

    if ($stmt === false) {
        destroySession();
    }

    $stmt->bind_param('is', $user_id, $token);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        destroySession();
    }

    $user = $result->fetch_assoc();
    if ($user['role'] !== 'admin') {
        destroySession();
        header('Location: ../404');
        exit;
    }

    session_regenerate_id(true);
}

checkAuth($conn);

$currentPage = isset($_GET['page']) ? $_GET['page'] : 'dashboard';
?>

<!DOCTYPE html>
<html lang="ar" data-theme="light">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/png" href="/assets/img/favicon.png">

    <link rel="stylesheet" href="/assets/css/all.min.css">
    <link rel="stylesheet" href="/assets/tailwind/tailwind.min.css">
    <link rel="stylesheet" href="/assets/daisy/full.min.css">
    <link rel="stylesheet" href="/assets/font_cairo.css">

    <script src="/assets/sweetalert/<EMAIL>"></script>


    <title>لوحة التحكم</title>

    <style>
        #sidebar {
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        #sidebar.open {
            transform: translateX(0);
        }

        @media (min-width: 768px) {
            #sidebar {
                position: fixed;
                height: 100%;
            }
        }

        .active a {
            background-color: #4A5568;
            color: #F7FAFC;
        }

        button:disabled {
            background-color: #4a5568;
            cursor: not-allowed;
        }

        .rtl {
            direction: rtl;
        }

        .ltr {
            direction: ltr;
        }
    </style>

</head>

<body class="bg-gray-100 text-gray-700 flex">

    <!-- Sidebar -->
    <div id="sidebar" class="bg-gray-900 text-gray-100 w-64 h-full fixed z-10 shadow-lg overflow-y-auto">
        <div class="p-6">
            <h2 class="text-2xl font-semibold mb-6 text-center">الأقسام</h2>
            <ul class="space-y-6">
                <!-- Home Section -->
                <li>
                    <a href="/" class="flex items-center p-2 my-10 text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white transition">
                        <i class="fas fa-home mr-3 text-green-500"></i>
                        <span class="text-green-500">الرئيسية</span>
                    </a>
                </li>

                <!-- Dashboard Section -->
                <li>
                    <a href="/admin/dashboard" class="flex items-center p-2 text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white transition">
                        <i class="fas fa-tachometer-alt fa-lg mr-3"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </li>

                <!-- Slides Section -->
                <li <?php if ($currentPage === 'slides') echo 'class="active"'; ?>>
                    <a href="?page=slides" class="flex items-center p-2 text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white transition">
                        <i class="fas fa-images fa-lg mr-3"></i>
                        <span>الشرائح</span>
                    </a>
                </li>

                <!-- Blog Section -->
                <li <?php if ($currentPage === 'blog') echo 'class="active"'; ?>>
                    <a href="?page=blog" class="flex items-center p-2 text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white transition">
                        <i class="fas fa-blog fa-lg mr-3"></i>
                        <span>المقالات</span>
                    </a>
                </li>

                <!-- Projects Section -->
                <li <?php if ($currentPage === 'project') echo 'class="active"'; ?>>
                    <a href="?page=project" class="flex items-center p-2 text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white transition">
                        <i class="fas fa-project-diagram fa-lg mr-3"></i>
                        <span>المشاريع</span>
                    </a>
                </li>

                <!-- Supporters Section -->
                <li <?php if ($currentPage === 'donations') echo 'class="active"'; ?>>
                    <a href="?page=donations" class="flex items-center p-2 text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white transition">
                        <i class="fas fa-hands-helping fa-lg mr-3"></i>
                        <span>الداعمين</span>
                    </a>
                </li>

                <!-- Supporters Section -->
                <li <?php if ($currentPage === 'currencies') echo 'class="active"'; ?>>
                    <a href="?page=currencies" class="flex items-center p-2 text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white transition">
                        <i class="fa-solid fa-money-bill fa-lg mr-3"></i>
                        <span>عملات التبرع</span>
                    </a>
                </li>

                <!-- Settings Section -->
                <li <?php if ($currentPage === 'settings') echo 'class="active"'; ?>>
                    <a href="?page=settings" class="flex items-center p-2 text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white transition">
                        <i class="fas fa-cog fa-lg mr-3"></i>
                        <span>الإعدادات</span>
                    </a>
                </li>

                <!-- Donation Wallets Section -->
                <li <?php if ($currentPage === 'bank') echo 'class="active"'; ?>>
                    <a href="?page=bank" class="flex items-center p-2 text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white transition">
                        <i class="fas fa-piggy-bank fa-lg mr-3"></i>
                        <span>محافظ التبرع</span>
                    </a>
                </li>

                <!-- Privacy Section -->
                <li <?php if ($currentPage === 'privacy') echo 'class="active"'; ?>>
                    <a href="?page=privacy" class="flex items-center p-2 text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white transition">
                        <i class="fas fa-lock fa-lg mr-3"></i>
                        <span>الخصوصية</span>
                    </a>
                </li>

                <!-- Logout Section -->
                <li>
                    <a href="/auth/api/logout.php" class="flex items-center p-2 text-gray-300 rounded-lg hover:bg-gray-800 hover:text-white transition">
                        <i class="fas fa-sign-out-alt fa-lg mr-3"></i>
                        <span>تسجيل خروج</span>
                    </a>
                </li>
            </ul>
        </div>
    </div>


    <!-- المحتوى الرئيسي -->
    <div class="flex-1 flex flex-col">
        <!-- شريط التنقل -->
        <nav class="bg-gray-300 p-4 flex justify-between items-center shadow-md">
            <div class="text-lg font-semibold">
                <?php
                switch ($currentPage) {
                    case 'dashboard':
                        echo 'لوحة التحكم';
                        break;
                    case 'slides':
                        echo 'إدارة الشرائح';
                        break;
                    case 'blog':
                        echo 'إدارة المقالات';
                        break;
                    case 'project':
                        echo 'إدارة المشاريع';
                        break;
                    case 'donations':
                        echo 'حوالات الداعمين';
                        break;
                    case 'currencies':
                        echo 'عملات التبرع';
                        break;
                    case 'settings':
                        echo 'الأعدادات';
                        break;
                    case 'bank':
                        echo 'إدارة المحافظ';
                        break;
                    case 'privacy':
                        echo 'إدارة الحساب';
                        break;
                    default:
                        echo 'لوحة التحكم';
                }
                ?>
            </div>

            <label class="btn btn-circle swap swap-rotate">
                <input id="sidebar-toggle" type="checkbox" />
                <!-- hamburger icon -->
                <svg
                    class="swap-off fill-current"
                    xmlns="http://www.w3.org/2000/svg"
                    width="32"
                    height="32"
                    viewBox="0 0 512 512">
                    <path d="M64,384H448V341.33H64Zm0-106.67H448V234.67H64ZM64,128v42.67H448V128Z" />
                </svg>
                <svg
                    class="swap-on fill-current"
                    xmlns="http://www.w3.org/2000/svg"
                    width="32"
                    height="32"
                    viewBox="0 0 512 512">
                    <polygon
                        points="400 145.49 366.51 112 256 222.51 145.49 112 112 145.49 222.51 256 112 366.51 145.49 400 256 289.49 366.51 400 400 366.51 289.49 256 400 145.49" />
                </svg>
            </label>

        </nav>

        <!-- محتوى الصفحة -->
        <div class="p-6 flex-1 bg-gray-100">
            <?php
            if ($currentPage === 'dashboard') {
                include 'sections/dash.php';
            } elseif ($currentPage === 'slides') {
                include 'sections/slides.php';
            } elseif ($currentPage === 'donations') {
                include 'sections/donations.php';
            } elseif ($currentPage === 'currencies') {
                include 'sections/currencies.php';
            } elseif ($currentPage === 'bank') {
                include 'sections/bank.php';
            } elseif ($currentPage === 'settings') {
                include 'sections/settings.php';
            } elseif ($currentPage === 'project') {
                include 'sections/project.php';
            } elseif ($currentPage === 'blog') {
                include 'sections/blog.php';
            } elseif ($currentPage === 'settings') {
                include 'sections/settings.php';
            } elseif ($currentPage === 'privacy') {
                include 'sections/privacy.php';
            }
            ?>
        </div>
    </div>

    <script>
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const sidebar = document.getElementById('sidebar');

        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('open');
        });
    </script>

</body>

</html>