<?php
// الاتصال بقاعدة البيانات
include '../../../config/conn.php';

// استرجاع بيانات المشروع المعدلة
$projectId = isset($_POST['id']) ? $_POST['id'] : '';
$title = isset($_POST['title']) ? $_POST['title'] : '';
$description = isset($_POST['description']) ? $_POST['description'] : '';
$amount = isset($_POST['amount']) ? $_POST['amount'] : '';
$current_donations = isset($_POST['current_donations']) ? $_POST['current_donations'] : ''; // تم تجميع حتى الآن
$project_type = isset($_POST['project_type']) ? $_POST['project_type'] : '';
$project_kafel = isset($_POST['project_kafel']) ? $_POST['project_kafel'] : '';
$is_urgent = isset($_POST['is_urgent']) ? $_POST['is_urgent'] : 'no';

// تحديد الاستجابة الافتراضية
$response = ['success' => false, 'message' => 'فشل في تحديث المشروع.'];

// التحقق من وجود المعرف
if (!empty($projectId)) {
    // استرجاع اسم الصورة القديمة من قاعدة البيانات
    $query = "SELECT image_path FROM projects WHERE id = '$projectId'";
    $result = mysqli_query($conn, $query);

    if ($result && mysqli_num_rows($result) > 0) {
        $project = mysqli_fetch_assoc($result);
        $oldImagePath = $project['image_path']; // اسم الصورة القديمة

        // بناء الاستعلام لتحديث المشروع
        $query = "UPDATE projects SET
                    title = '$title',
                    description = '$description',
                    remaining_amount = '$amount',
                    current_donations = '$current_donations',
                    project_type = '$project_type',
                    project_kafel = '$project_kafel',
                    is_urgent = '$is_urgent'
                  WHERE id = '$projectId'";

        // إضافة صورة إذا تم رفعها
        if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
            // حذف الصورة القديمة إذا كانت موجودة
            if (!empty($oldImagePath) && file_exists('../../../assets/img/project/' . $oldImagePath)) {
                unlink('../../../assets/img/project/' . $oldImagePath);
            }

            // إنشاء اسم جديد للصورة باستخدام uniqid() وامتداد الصورة
            $imageExtension = pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION);
            $newImageName = 'project_' . uniqid() . '.' . $imageExtension;

            // تعيين مسار الصورة الجديدة
            $newImagePath = '../../../assets/img/project/' . $newImageName;
            move_uploaded_file($_FILES['image']['tmp_name'], $newImagePath);

            // تحديث الاستعلام مع اسم الصورة الجديد
            $query = "UPDATE projects SET
                        title = '$title',
                        description = '$description',
                        remaining_amount = '$amount',
                        current_donations = '$current_donations',
                        project_type = '$project_type',
                        project_kafel = '$project_kafel',
                        is_urgent = '$is_urgent',
                        image_path = '$newImageName'
                      WHERE id = '$projectId'";
        }

        // تنفيذ الاستعلام
        $result = mysqli_query($conn, $query);

        if ($result) {
            $response = ['success' => true, 'message' => 'تم تحديث المشروع بنجاح.'];
        } else {
            $response = ['success' => false, 'message' => 'حدث خطأ في قاعدة البيانات.'];
        }
    } else {
        $response = ['success' => false, 'message' => 'المشروع غير موجود.'];
    }
} else {
    $response = ['success' => false, 'message' => 'المعرف غير موجود.'];
}

// إرسال الاستجابة بتنسيق JSON
header('Content-Type: application/json');
echo json_encode($response);
?>
