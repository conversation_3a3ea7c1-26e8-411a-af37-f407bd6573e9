<?php
// تمكين عرض الأخطاء
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// الاتصال بقاعدة البيانات
include '../../../config/conn.php';

// التحقق من الاتصال بقاعدة البيانات
if (!$conn) {
    die("فشل الاتصال بقاعدة البيانات: " . mysqli_connect_error());
}

echo "تم الاتصال بقاعدة البيانات بنجاح.<br>";

// التحقق مما إذا كان العمود موجودًا بالفعل
$check_query = "SHOW COLUMNS FROM wallet_addresses LIKE 'sort_order'";
echo "استعلام التحقق: " . $check_query . "<br>";

$check_result = mysqli_query($conn, $check_query);

if (!$check_result) {
    die("فشل استعلام التحقق: " . mysqli_error($conn) . "<br>");
}

echo "تم تنفيذ استعلام التحقق بنجاح.<br>";
echo "عدد الصفوف: " . mysqli_num_rows($check_result) . "<br>";

if (mysqli_num_rows($check_result) == 0) {
    // إضافة العمود إذا لم يكن موجودًا
    $alter_query = "ALTER TABLE wallet_addresses ADD COLUMN sort_order INT DEFAULT NULL";
    echo "استعلام إضافة العمود: " . $alter_query . "<br>";

    $alter_result = mysqli_query($conn, $alter_query);

    if (!$alter_result) {
        die("فشل استعلام إضافة العمود: " . mysqli_error($conn) . "<br>");
    }

    echo "تم إضافة العمود بنجاح.<br>";

    // تحديث قيم الترتيب الافتراضية بناءً على معرف الصف
    $update_query = "UPDATE wallet_addresses SET sort_order = id";
    echo "استعلام تحديث القيم الافتراضية: " . $update_query . "<br>";

    $update_result = mysqli_query($conn, $update_query);

    if (!$update_result) {
        echo "فشل استعلام تحديث القيم الافتراضية: " . mysqli_error($conn) . "<br>";
    } else {
        echo "تم تحديث القيم الافتراضية بنجاح.<br>";
        echo "تم إضافة عمود sort_order وتحديث القيم الافتراضية بنجاح.<br>";
    }
} else {
    echo "العمود sort_order موجود بالفعل.<br>";

    // عرض معلومات العمود
    $column_info_query = "SHOW COLUMNS FROM wallet_addresses WHERE Field = 'sort_order'";
    $column_info_result = mysqli_query($conn, $column_info_query);

    if ($column_info_result && mysqli_num_rows($column_info_result) > 0) {
        $column_info = mysqli_fetch_assoc($column_info_result);
        echo "معلومات العمود: <pre>" . print_r($column_info, true) . "</pre><br>";
    }
}
?>
