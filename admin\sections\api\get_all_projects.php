<?php
// الاتصال بقاعدة البيانات
include '../../../config/conn.php';


// تحديد الاستجابة الافتراضية
$response = ['success' => false, 'message' => 'فشل في استرجاع المشاريع.'];

// استعلام لجلب جميع المشاريع مع الأعمدة المتاحة
$query = "SELECT * FROM projects";

// تنفيذ الاستعلام
$result = mysqli_query($conn, $query);

if ($result) {
    $projects = [];

    // التحقق من وجود بيانات في الاستعلام
    while ($row = mysqli_fetch_assoc($result)) {
        // إنشاء مصفوفة للمشروع مع التحقق من وجود الحقول
        $project = [
            'id' => $row['id'],
            'title' => $row['title'],
            'project_type' => isset($row['project_type']) ? $row['project_type'] : 'always',
            'description' => $row['description'],
            'current_donations' => isset($row['current_donations']) ? $row['current_donations'] : 0,
            'remaining_amount' => isset($row['remaining_amount']) ? $row['remaining_amount'] : 0,
            'created_at' => isset($row['created_at']) ? $row['created_at'] : date('Y-m-d H:i:s'),
            'image_path' => isset($row['image_path']) ? $row['image_path'] : ''
        ];

        // إضافة الحقول الاختيارية إذا كانت موجودة
        if (isset($row['project_kafel'])) {
            $project['project_kafel'] = $row['project_kafel'];
        } else {
            $project['project_kafel'] = 'no';
        }

        if (isset($row['is_urgent'])) {
            $project['is_urgent'] = $row['is_urgent'];
        } else {
            $project['is_urgent'] = 'no';
        }

        if (isset($row['duration_days'])) {
            $project['duration_days'] = $row['duration_days'];
        }

        if (isset($row['start_date'])) {
            $project['start_date'] = $row['start_date'];
        } else {
            $project['start_date'] = isset($row['created_at']) ? $row['created_at'] : date('Y-m-d H:i:s');
        }

        $projects[] = $project;
    }

    // إذا كانت هناك مشاريع تم استرجاعها بنجاح
    if (count($projects) > 0) {
        $response = [
            'success' => true,
            'projects' => $projects
        ];
    } else {
        $response = ['success' => false, 'message' => 'لا توجد مشاريع لعرضها.'];
    }
} else {
    $response = ['success' => false, 'message' => 'حدث خطأ في قاعدة البيانات.'];
}

// إرسال الاستجابة بتنسيق JSON
header('Content-Type: application/json');
echo json_encode($response);
?>
