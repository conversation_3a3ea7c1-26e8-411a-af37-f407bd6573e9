<?php
require '../../../config/conn.php';

header('Content-Type: application/json');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit;
}

// التحقق من وجود البيانات المطلوبة
if (!isset($_POST['id']) || !isset($_POST['name_ar']) || !isset($_POST['symbol']) || !isset($_POST['value'])) {
    echo json_encode(['success' => false, 'message' => 'جميع الحقول مطلوبة']);
    exit;
}

// تنظيف وتحقق من البيانات
$id = intval($_POST['id']);
$name_ar = trim($_POST['name_ar']);
$symbol = trim($_POST['symbol']);
$value = floatval($_POST['value']);

if ($id <= 0 || empty($name_ar) || empty($symbol) || $value <= 0) {
    echo json_encode(['success' => false, 'message' => 'يرجى إدخال بيانات صحيحة']);
    exit;
}

// التحقق من عدم وجود رمز العملة مسبقاً لعملة أخرى
$checkQuery = "SELECT id FROM currencies WHERE symbol = ? AND id != ?";
$stmt = $conn->prepare($checkQuery);
$stmt->bind_param('si', $symbol, $id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    echo json_encode(['success' => false, 'message' => 'رمز العملة موجود بالفعل لعملة أخرى']);
    $stmt->close();
    exit;
}

// تحديث بيانات العملة
$updateQuery = "UPDATE currencies SET name_ar = ?, symbol = ?, value = ? WHERE id = ?";
$stmt = $conn->prepare($updateQuery);
$stmt->bind_param('ssdi', $name_ar, $symbol, $value, $id);

if ($stmt->execute()) {
    echo json_encode(['success' => true, 'message' => 'تم تحديث بيانات العملة بنجاح']);
} else {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء تحديث بيانات العملة: ' . $conn->error]);
}

$stmt->close();
$conn->close();
