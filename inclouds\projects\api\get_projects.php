<?php
// الاتصال بقاعدة البيانات
include '../../config/conn.php';

// تحديد الاستجابة الافتراضية
$response = ['success' => false, 'message' => 'فشل في استرجاع المشاريع.'];

// التحقق من وجود نوع المشروع في الطلب
$type = isset($_GET['type']) ? $_GET['type'] : 'always'; // النوع الافتراضي هو 'always'

// التأكد من أن النوع يطابق القيم المسموحة
$allowed_types = ['always', 'renewed'];
if (!in_array($type, $allowed_types)) {
    echo json_encode(['success' => false, 'message' => 'نوع المشاريع غير معروف']);
    exit;
}

// استعلام لجلب المشاريع بناءً على النوع
$query = "SELECT * FROM projects WHERE project_type = '$type'";

// تنفيذ الاستعلام
$result = mysqli_query($conn, $query);

if ($result) {
    $projects = [];

    // التحقق من وجود بيانات في الاستعلام
    while ($row = mysqli_fetch_assoc($result)) {
        $projects[] = [
            'id' => $row['id'],
            'title' => $row['title'],
            'project_type' => $row['project_type'],
            'project_kafel' => $row['project_kafel'],
            'description' => $row['description'],
            'current_donations' => $row['current_donations'],
            'remaining_amount' => $row['remaining_amount'],
            'created_at' => $row['created_at'],  // تاريخ الإنشاء (created_at)
            'duration_days' => $row['duration_days'],
            'image_path' => $row['image_path'],
            'is_urgent' => $row['is_urgent'] // إضافة حقل المشاريع العاجلة
        ];
    }

    // إذا كانت هناك مشاريع تم استرجاعها بنجاح
    if (count($projects) > 0) {
        $response = [
            'success' => true,
            'projects' => $projects
        ];
    } else {
        $response = ['success' => false, 'message' => 'لا توجد مشاريع لعرضها.'];
    }
} else {
    $response = ['success' => false, 'message' => 'حدث خطأ في قاعدة البيانات.'];
}

// إرسال الاستجابة بتنسيق JSON
header('Content-Type: application/json');
echo json_encode($response);
?>
