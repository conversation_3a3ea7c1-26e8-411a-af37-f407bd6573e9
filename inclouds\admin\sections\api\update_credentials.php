<?php
session_start();
include '../../../config/conn.php';

// تأكد من أن المستخدم مسجل الدخول
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'غير مسموح بالوصول']);
    exit;
}

// استقبال البيانات من النموذج
$newEmail = $_POST['newEmail'] ?? '';
$newPassword = $_POST['newPassword'] ?? '';

// التحقق من صحة البيانات
if (empty($newEmail) || empty($newPassword)) {
    echo json_encode(['success' => false, 'message' => 'جميع الحقول مطلوبة']);
    exit;
}

// تشفير كلمة المرور باستخدام Argon2
$hashedPassword = password_hash($newPassword, PASSWORD_ARGON2I);

// تحديث البريد الإلكتروني وكلمة المرور في قاعدة البيانات
$userId = $_SESSION['user_id'];
$stmt = $conn->prepare("UPDATE users SET email = ?, password = ? WHERE user_id = ?");
$stmt->bind_param("ssi", $newEmail, $hashedPassword, $userId);

if ($stmt->execute()) {
    echo json_encode(['success' => true, 'message' => 'تم تحديث البيانات بنجاح']);
} else {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء تحديث البيانات: ' . $stmt->error]);
}

// إغلاق الاتصال
$stmt->close();
$conn->close();
