<?php
// استيراد ملف الاتصال بقاعدة البيانات
include '../../../config/conn.php';

// update_article.php
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id = $_POST['id'];
    $title = $_POST['title'];
    $views = $_POST['views'];
    $content = $_POST['content'];
    $image = isset($_FILES['image']) ? $_FILES['image'] : null;

    // استرجاع مسار الصورة القديمة من قاعدة البيانات
    $query = "SELECT image FROM articles WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $stmt->bind_result($oldImagePath);
    $stmt->fetch();
    $stmt->close();

    // التحقق من رفع صورة جديدة
    if ($image && $image['error'] == 0) {
        // مسار الصورة الجديدة
        $imagePath = '../../../assets/img/blog/' . $image['name'];

        // حذف الصورة القديمة إذا كانت موجودة
        if ($oldImagePath && file_exists($oldImagePath)) {
            unlink($oldImagePath); // حذف الصورة القديمة من المجلد
        }

        // رفع الصورة الجديدة
        move_uploaded_file($image['tmp_name'], $imagePath);
    } else {
        // إذا لم يتم رفع صورة جديدة، استخدم الصورة القديمة
        $imagePath = $oldImagePath;
    }

    // تحديث المقال في قاعدة البيانات
    $query = "UPDATE articles SET title = ?, views = ?, content = ?, image = ? WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('ssssi', $title, $views, $content, $imagePath, $id);
    $stmt->execute();

    if ($stmt->affected_rows > 0) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء التعديل']);
    }
}
?>
