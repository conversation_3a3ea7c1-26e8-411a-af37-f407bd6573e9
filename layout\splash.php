<style>
    /* تعريف الحركات */
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes scaleIn {
        from { transform: scale(0.9); opacity: 0; }
        to { transform: scale(1); opacity: 1; }
    }

    @keyframes slideUp {
        from { transform: translateY(30px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }

    @keyframes shimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
    }

    /* تصميم الشاشة الافتتاحية */
    #splash-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 9999;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #c3875d;
        font-family: 'Cairo', sans-serif;
        direction: rtl;
    }

    .splash-container {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        overflow: hidden;
    }

    .splash-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle at center, rgba(195, 135, 93, 0.8) 0%, rgba(118, 77, 47, 0.95) 100%);
        z-index: 1;
    }

    .splash-content {
        position: relative;
        z-index: 2;
        text-align: center;
        padding: 2rem;
        max-width: 600px;
        background-color: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.2);
        animation: scaleIn 0.8s ease forwards;
    }

    .splash-logo {
        width: 120px;
        height: auto;
        margin: 0 auto 1.5rem;
        display: block;
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
        animation: scaleIn 1s ease forwards;
    }

    .splash-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: #fff;
        margin-bottom: 1rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        animation: slideUp 0.8s ease forwards;
        animation-delay: 0.2s;
        opacity: 0;
    }

    .splash-subtitle {
        font-size: 1.25rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 2rem;
        line-height: 1.6;
        animation: slideUp 0.8s ease forwards;
        animation-delay: 0.4s;
        opacity: 0;
    }

    .splash-cta {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        background-color: #fff;
        color: #c3875d;
        font-size: 1.1rem;
        font-weight: 600;
        padding: 0.75rem 2rem;
        border-radius: 50px;
        border: none;
        cursor: pointer;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
        animation: slideUp 0.8s ease forwards;
        animation-delay: 0.6s;
        opacity: 0;
        position: relative;
        overflow: hidden;
    }

    .splash-cta:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        background-color: #f8f8f8;
    }

    .splash-cta:active {
        transform: translateY(-1px);
    }

    .splash-cta::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            rgba(255,255,255,0) 0%,
            rgba(255,255,255,0.5) 50%,
            rgba(255,255,255,0) 100%);
        background-size: 200% 100%;
        animation: shimmer 2s infinite;
        z-index: 1;
    }

    .splash-cta-text, .splash-cta-icon {
        position: relative;
        z-index: 2;
    }

    .splash-cta-icon {
        color: #c3875d;
    }

    .splash-progress {
        position: absolute;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        width: 200px;
        height: 4px;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 4px;
        overflow: hidden;
        z-index: 2;
    }

    .splash-progress-bar {
        height: 100%;
        width: 0;
        background-color: #fff;
        border-radius: 4px;
        transition: width 0.1s linear;
    }

    .splash-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;
        opacity: 0.1;
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    .splash-decoration {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: 1;
        pointer-events: none;
    }

    .splash-decoration-item {
        position: absolute;
        opacity: 0.15;
        color: #fff;
        font-size: 2rem;
    }

    .decoration-1 {
        top: 15%;
        left: 10%;
        animation: fadeIn 1s ease forwards, float 3s ease-in-out infinite;
        animation-delay: 0.2s;
        opacity: 0;
    }

    .decoration-2 {
        top: 20%;
        right: 10%;
        animation: fadeIn 1s ease forwards, float 4s ease-in-out infinite;
        animation-delay: 0.4s;
        opacity: 0;
    }

    .decoration-3 {
        bottom: 20%;
        left: 15%;
        animation: fadeIn 1s ease forwards, float 3.5s ease-in-out infinite;
        animation-delay: 0.6s;
        opacity: 0;
    }

    .decoration-4 {
        bottom: 25%;
        right: 15%;
        animation: fadeIn 1s ease forwards, float 4.5s ease-in-out infinite;
        animation-delay: 0.8s;
        opacity: 0;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-15px); }
    }

    /* تحسينات للأجهزة المحمولة */
    @media (max-width: 768px) {
        .splash-content {
            max-width: 90%;
            padding: 1.5rem;
        }

        .splash-title {
            font-size: 2rem;
        }

        .splash-subtitle {
            font-size: 1rem;
        }

        .splash-logo {
            width: 100px;
        }
    }
</style>

<div id="splash-screen">
    <div class="splash-container">
        <div class="splash-background"></div>
        <div class="splash-overlay"></div>

        <div class="splash-decoration">
            <i class="fas fa-heart splash-decoration-item decoration-1"></i>
            <i class="fas fa-hands-helping splash-decoration-item decoration-2"></i>
            <i class="fas fa-home splash-decoration-item decoration-3"></i>
            <i class="fas fa-medkit splash-decoration-item decoration-4"></i>
        </div>

        <div class="splash-content">
            <img src="/assets/img/favicon.png" alt="logo" class="splash-logo">
            <h1 class="splash-title">معاً من أجل غزة</h1>
            <p class="splash-subtitle">تبرعاتكم تصنع الفرق في حياة المحتاجين في فلسطين</p>
        </div>

        <div class="splash-progress">
            <div class="splash-progress-bar" id="splash-progress-bar"></div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحديث شريط التقدم
        const progressBar = document.getElementById('splash-progress-bar');
        const splashScreen = document.getElementById('splash-screen');
        const splashCta = document.querySelector('.splash-cta');

        let progress = 0;
        const totalDuration = 3500; // 3.5 ثوانٍ
        const interval = 30; // تحديث كل 30 مللي ثانية
        const increment = (interval / totalDuration) * 100;

        const progressInterval = setInterval(() => {
            progress += increment;
            progressBar.style.width = `${Math.min(progress, 100)}%`;

            if (progress >= 100) {
                clearInterval(progressInterval);
                setTimeout(() => {
                    // إخفاء الشاشة بتأثير تلاشي
                    splashScreen.style.opacity = '0';
                    splashScreen.style.transition = 'opacity 0.5s ease';

                    // إزالة الشاشة من DOM بعد انتهاء التأثير
                    setTimeout(() => {
                        splashScreen.style.display = 'none';
                    }, 500);
                }, 300);
            }
        }, interval);

        // التمرير إلى قسم المشاريع عند النقر على زر التبرع
        
        if (splashCta) {
                    splashCta.addEventListener('click', () => {
            clearInterval(progressInterval);
            splashScreen.style.opacity = '0';
            splashScreen.style.transition = 'opacity 0.5s ease';

            setTimeout(() => {
                splashScreen.style.display = 'none';
                // التمرير إلى قسم المشاريع
                const projectsSection = document.querySelector('#projects-section');
                if (projectsSection) {
                    projectsSection.scrollIntoView({ behavior: 'smooth' });
                }
            }, 500);
        });
        }
        

        
        
        
    });
</script>
