<!DOCTYPE html>
<html lang="ar" data-theme="light">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link rel="icon" type="image/png" href="/assets/img/favicon.png">

    <title>المدونة</title>

    <style>
        .rtl {
            direction: rtl;
        }
    </style>
</head>

<?php
require '../layout/header.php';
require '../config/conn.php';

function createSlug($string)
{
    return urlencode(preg_replace('/\s+/u', '-', trim($string)));
}

// جلب المقالات المنشورة باستخدام الاستعلامات المحضرة
$sql = "SELECT * FROM articles WHERE status = 'published' ORDER BY created_at DESC";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_execute($stmt);
$articles = mysqli_stmt_get_result($stmt); 

// التأكد من وجود مقالات
if (mysqli_num_rows($articles) > 0): ?>
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8">المقالات</h1>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 rtl">
            <?php while ($row = mysqli_fetch_assoc($articles)): ?>
                <?php
                // تعيين الصورة الافتراضية في حال عدم وجود صورة
                $image = !empty($row['image']) ? '../assets/img/blog/' . $row['image'] : '../assets/img/default.jpg';
                if (!file_exists($image)) {
                    echo "الصورة غير موجودة: " . $image;
                }
                ?>
                <div class="card bg-white shadow-lg rounded-lg overflow-hidden flex flex-col h-full">
                    <!-- صورة الكرت -->
                    <img src="<?= $image ?>" alt="<?= htmlspecialchars($row['title']) ?>" class="w-full h-48 object-cover">
                    <div class="p-4 flex flex-col flex-grow">
                        <!-- عنوان المقالة -->
                        <h2 class="text-xl font-semibold mb-2 text-right flex-none"><?= htmlspecialchars($row['title']) ?></h2>
                        <!-- محتوى المقالة (مقتطف) -->
                        <p class="text-gray-700 mb-4 text-right flex-grow"><?= strip_tags(substr($row['content'], 0, 100)) ?>...</p>
                        <!-- بيانات إضافية (تاريخ المشاهدة) -->
                        <div class="text-gray-500 text-sm mb-4 flex flex-row-reverse justify-between">
                            <span class="flex gap-3 items-center"><?= date('d-m-Y', strtotime($row['created_at'])) ?> <i class="fa fa-calendar" style="color: #c3875d;" aria-hidden="true"></i></span>
                            <span class="flex gap-3 items-center"><?= $row['views'] ?> مشاهدة <i class="fa fa-eye" style="color: #c3875d;" aria-hidden="true"></i></span>
                        </div>
                        <!-- زر "قراءة المزيد" -->
                        <a href="article/<?= $row['id'] ?>/<?= createSlug($row['title']) ?>" class="btn w-full" style="background-color:#c3875d; color:#fff;">قراءة المزيد</a>
                    </div>
                </div>
            <?php endwhile; ?>
        </div>

    </div>
<?php else: ?>
    <p class="text-center text-gray-600">لا توجد مقالات متاحة حالياً.</p>
<?php endif; ?>

<body class="bg-gray-100">
    <?php include '../layout/footer.php'; ?>
</body>

</html>