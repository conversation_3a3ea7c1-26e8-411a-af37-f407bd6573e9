<?php
// الاتصال بقاعدة البيانات
include '../../../config/conn.php';

// استرجاع معرف المشروع من الرابط (إذا كان موجودًا)
$projectId = isset($_GET['id']) ? $_GET['id'] : '';

// تحديد الاستجابة الافتراضية
$response = ['success' => false, 'message' => 'فشل في استرجاع المشروع.'];

// إذا كان هناك معرف مشروع، نقوم باسترجاع المشروع المحدد فقط
if ($projectId) {
    // استعلام لجلب المشروع المحدد باستخدام المعرف
    $query = "SELECT * FROM projects WHERE id = ?";

    if ($stmt = mysqli_prepare($conn, $query)) {
        // ربط المعاملات
        mysqli_stmt_bind_param($stmt, "i", $projectId);
        // تنفيذ الاستعلام
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);

        if ($result && mysqli_num_rows($result) > 0) {
            // استرجاع المشروع
            $project = mysqli_fetch_assoc($result);

            // إنشاء مصفوفة للمشروع مع التحقق من وجود الحقول
            $projectData = [
                'id' => $project['id'],
                'title' => $project['title'],
                'description' => $project['description'],
                'amount' => isset($project['remaining_amount']) ? $project['remaining_amount'] : 0,
                'image_path' => isset($project['image_path']) ? $project['image_path'] : ''
            ];

            // إضافة الحقول الاختيارية إذا كانت موجودة
            if (isset($project['project_type'])) {
                $projectData['project_type'] = $project['project_type'];
            } else {
                $projectData['project_type'] = 'always';
            }

            if (isset($project['current_donations'])) {
                $projectData['current_donations'] = $project['current_donations'];
            } else {
                $projectData['current_donations'] = 0;
            }

            if (isset($project['project_kafel'])) {
                $projectData['project_kafel'] = $project['project_kafel'];
            } else {
                $projectData['project_kafel'] = 'no';
            }

            if (isset($project['is_urgent'])) {
                $projectData['is_urgent'] = $project['is_urgent'];
            } else {
                $projectData['is_urgent'] = 'no';
            }

            if (isset($project['start_date'])) {
                $projectData['start_date'] = $project['start_date'];
            } else {
                $projectData['start_date'] = isset($project['created_at']) ? $project['created_at'] : date('Y-m-d H:i:s');
            }

            // إذا تم العثور على المشروع
            $response = [
                'success' => true,
                'project' => $projectData
            ];
        } else {
            $response = ['success' => false, 'message' => 'لم يتم العثور على المشروع'];
        }

        // غلق الاتصال بالـ Prepared Statement
        mysqli_stmt_close($stmt);
    } else {
        $response = ['success' => false, 'message' => 'فشل في تنفيذ الاستعلام'];
    }
} else {
    $response = ['success' => false, 'message' => 'لم يتم تحديد معرف المشروع'];
}

// إرسال الاستجابة بتنسيق JSON
header('Content-Type: application/json');
echo json_encode($response);
?>
