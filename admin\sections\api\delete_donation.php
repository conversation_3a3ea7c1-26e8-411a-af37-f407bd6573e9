<?php
require '../../../config/conn.php';

header('Content-Type: application/json');

// السماح بجميع طرق الطلب (لأغراض التطوير فقط)
header("Access-Control-Allow-Methods: DELETE");

// للتحقق من طريقة الطلب في بيئة مشتركة
$method = $_SERVER['REQUEST_METHOD'];
if ($method === 'POST' && isset($_POST['_method'])) {
    $method = strtoupper($_POST['_method']);
}

if ($method === 'DELETE') {
    $donationId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    
    if ($donationId > 0) {
        try {
            // بدء transaction
            $conn->begin_transaction();
            
            // جلب أسماء الملفات المرتبطة بالتبرع
            $sql = "SELECT proof_image FROM donations WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $donationId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($row = $result->fetch_assoc()) {
                $proofImages = explode(',', $row['proof_image']); // تقسيم الأسماء إلى مصفوفة
                
                foreach ($proofImages as $fileName) {
                    $filePath = '../../../assets/img/proof/' . trim($fileName);
                    if (file_exists($filePath)) {
                        if (!unlink($filePath)) {
                            throw new Exception("فشل في حذف ملف: " . htmlspecialchars($fileName));
                        }
                    }
                }
            }
            
            // حذف التبرع من قاعدة البيانات
            $sql = "DELETE FROM donations WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $donationId);
            
            if ($stmt->execute()) {
                $conn->commit();
                echo json_encode(['success' => true]);
            } else {
                throw new Exception("فشل في حذف التبرع من قاعدة البيانات");
            }
        } catch (Exception $e) {
            $conn->rollback();
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'معرف التبرع غير صحيح'
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'طريقة الطلب غير مسموحة. الطريقة المستخدمة: ' . $method
    ]);
}

$conn->close();
?>
